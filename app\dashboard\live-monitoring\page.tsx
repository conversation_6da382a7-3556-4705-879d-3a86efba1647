"use client";

import React, { useCallback } from "react";
import { LiveStreamCard } from "@/components/video/LiveStreamCard";
import { NFMTimelineComplete } from "@/components/timeline/NFMTimelineComplete";
import { useProjectContext } from "@/components/contexts/ProjectContext";
import { VideoTimelineProvider } from "@/components/contexts/VideoTimelineContext";

export default function LiveMonitoringPage() {
  console.log('[LiveMonitoringPage] RENDER - Component rendering')

  // Use project context for basic info
  const { currentProject, events, modalities } = useProjectContext();
  console.log('[LiveMonitoringPage] PROJECT CONTEXT - currentProject:', !!currentProject);

  // Memoize callback functions to prevent recreation and unnecessary re-renders
  const handleVideoSeek = useCallback((time: number) => {
    console.debug("[PAGE] Video seek to:", time);
  }, []);

  const handleTimelineSeek = useCallback((time: number) => {
    console.debug("[PAGE] Timeline seek to:", time);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    console.debug("[PAGE] Play state changed:", playing);
  }, []);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    console.debug("[PAGE] Playback rate changed:", rate);
  }, []);

  console.log('[LiveMonitoringPage] CALLBACKS - Created callbacks:', {
    handleVideoSeek: !!handleVideoSeek,
    handleTimelineSeek: !!handleTimelineSeek,
    handlePlayStateChange: !!handlePlayStateChange,
    handlePlaybackRateChange: !!handlePlaybackRateChange
  })

  // Show loading state while data is loading
  if (!currentProject) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <VideoTimelineProvider
      initialDuration={3600} // 1 hour default
      onVideoSeek={handleVideoSeek}
      onTimelineSeek={handleTimelineSeek}
      onPlayStateChange={handlePlayStateChange}
      onPlaybackRateChange={handlePlaybackRateChange}
      events={events}
      modalities={modalities}
    >
      <div className="flex flex-col space-y-4">
        {/* Video Stream - Optimized for viewport */}
        <LiveStreamCard
          streamPath="http://localhost:8888/test_stream/index.m3u8"
          isRecording={false}
          operatingRoom={currentProject.operatingRoom}
          maxHeight="45vh"
          className="flex-shrink-0"
          cardClassName="gap-2 px-2 pb-2"
          controls={false}
          muted={false}
          volume={1}
        />

        {/* Timeline Section - Optimized for remaining viewport */}
        <NFMTimelineComplete
          maxHeight="calc(55vh - 2rem)"
          className="flex-1 min-h-0"
          cardClassName="gap-2 px-2 pb-2"
        />
      </div>
    </VideoTimelineProvider>
  );
}
