"use client";

import React, { useCallback, useState } from "react";
import { LiveStreamCard } from "@/components/video/LiveStreamCard";
import { NFMTimelineComplete } from "@/components/timeline/NFMTimelineComplete";
import { EventCreationBar } from "@/components/events/EventCreationBar";
import { EventEditForm } from "@/components/events/EventEditForm";
import { useProjectContext } from "@/components/contexts/ProjectContext";
import { VideoTimelineProvider } from "@/components/contexts/VideoTimelineContext";

export default function LiveMonitoringPage() {
  console.log('[LiveMonitoringPage] RENDER - Component rendering')

  // Use project context for basic info
  const { currentProject, events, modalities } = useProjectContext();
  console.log('[LiveMonitoringPage] PROJECT CONTEXT - currentProject:', !!currentProject);

  // State for event editing and focus mode
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [focusMode, setFocusMode] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // Memoize callback functions to prevent recreation and unnecessary re-renders
  const handleVideoSeek = useCallback((time: number) => {
    console.debug("[PAGE] Video seek to:", time);
  }, []);

  const handleTimelineSeek = useCallback((time: number) => {
    console.debug("[PAGE] Timeline seek to:", time);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    console.debug("[PAGE] Play state changed:", playing);
  }, []);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    console.debug("[PAGE] Playback rate changed:", rate);
  }, []);

  // Event editing handlers
  const handleEventEdit = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  const handleEditFormSave = useCallback((eventId: string) => {
    console.log('Event saved:', eventId);
    // Form will close automatically
  }, []);

  const handleEditFormDelete = useCallback((eventId: string) => {
    console.log('Event deleted:', eventId);
    // Form will close automatically
  }, []);

  console.log('[LiveMonitoringPage] CALLBACKS - Created callbacks:', {
    handleVideoSeek: !!handleVideoSeek,
    handleTimelineSeek: !!handleTimelineSeek,
    handlePlayStateChange: !!handlePlayStateChange,
    handlePlaybackRateChange: !!handlePlaybackRateChange,
    handleEventEdit: !!handleEventEdit
  })

  // Show loading state while data is loading
  if (!currentProject) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <VideoTimelineProvider
      initialDuration={3600} // 1 hour default
      onVideoSeek={handleVideoSeek}
      onTimelineSeek={handleTimelineSeek}
      onPlayStateChange={handlePlayStateChange}
      onPlaybackRateChange={handlePlaybackRateChange}
      events={events}
      modalities={modalities}
    >
      {/* Masonry-style Layout */}
      <div className="space-y-4">

        {/* Top Row: Video + Event Creation (Landscape) / Video Only (Portrait) */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* Video Stream */}
          <div className="flex-1 min-w-0">
            <LiveStreamCard
              streamPath="http://localhost:8888/test_stream/index.m3u8"
              isRecording={false}
              operatingRoom={currentProject.operatingRoom}
              maxHeight={focusMode ? "80vh" : "calc(50vh - 2rem)"}
              className="flex-shrink-0"
              cardClassName="gap-2 px-2 pb-2"
              controls={false}
              muted={false}
              volume={1}
              focusMode={focusMode}
              onFocusModeToggle={setFocusMode}
              maintainAspectRatio={true}
              aspectRatio="16/9"
            />
          </div>

          {/* Event Creation Sidebar - Landscape Mode (md+ breakpoint) */}
          <div className="hidden md:block md:w-72 lg:w-80 xl:w-96 flex-shrink-0">
            <EventCreationBar className="w-full h-fit" />
          </div>
        </div>

        {/* Event Creation Bar - Portrait Mode (horizontal layout) */}
        <div className="md:hidden">
          <EventCreationBar className="w-full" />
        </div>

        {/* Timeline Section - Full Width */}
        <div className="w-full">
          <NFMTimelineComplete
            maxHeight={focusMode ? "calc(20vh - 1rem)" : "calc(40vh - 2rem)"}
            className="w-full"
            cardClassName="gap-2 px-2 pb-2"
            onEventEdit={handleEventEdit}
            showHeader={!focusMode}
            showControls={!focusMode}
          />
        </div>
      </div>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </VideoTimelineProvider>
  );
}
