"use client";

import React, { useCallback, useState } from "react";
import { LiveStreamCard } from "@/components/video/LiveStreamCard";
import { NFMTimelineComplete } from "@/components/timeline/NFMTimelineComplete";
import { EventCreationBar } from "@/components/events/EventCreationBar";
import { EventEditForm } from "@/components/events/EventEditForm";
import { useProjectContext } from "@/components/contexts/ProjectContext";
import { VideoTimelineProvider } from "@/components/contexts/VideoTimelineContext";

export default function LiveMonitoringPage() {
  console.log('[LiveMonitoringPage] RENDER - Component rendering')

  // Use project context for basic info
  const { currentProject, events, modalities } = useProjectContext();
  console.log('[LiveMonitoringPage] PROJECT CONTEXT - currentProject:', !!currentProject);

  // State for event editing
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // Memoize callback functions to prevent recreation and unnecessary re-renders
  const handleVideoSeek = useCallback((time: number) => {
    console.debug("[PAGE] Video seek to:", time);
  }, []);

  const handleTimelineSeek = useCallback((time: number) => {
    console.debug("[PAGE] Timeline seek to:", time);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    console.debug("[PAGE] Play state changed:", playing);
  }, []);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    console.debug("[PAGE] Playback rate changed:", rate);
  }, []);

  // Event editing handlers
  const handleEventEdit = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  const handleEditFormSave = useCallback((eventId: string) => {
    console.log('Event saved:', eventId);
    // Form will close automatically
  }, []);

  const handleEditFormDelete = useCallback((eventId: string) => {
    console.log('Event deleted:', eventId);
    // Form will close automatically
  }, []);

  console.log('[LiveMonitoringPage] CALLBACKS - Created callbacks:', {
    handleVideoSeek: !!handleVideoSeek,
    handleTimelineSeek: !!handleTimelineSeek,
    handlePlayStateChange: !!handlePlayStateChange,
    handlePlaybackRateChange: !!handlePlaybackRateChange,
    handleEventEdit: !!handleEventEdit
  })

  // Show loading state while data is loading
  if (!currentProject) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <VideoTimelineProvider
      initialDuration={3600} // 1 hour default
      onVideoSeek={handleVideoSeek}
      onTimelineSeek={handleTimelineSeek}
      onPlayStateChange={handlePlayStateChange}
      onPlaybackRateChange={handlePlaybackRateChange}
      events={events}
      modalities={modalities}
    >
      {/* Responsive Layout Container */}
      <div className="flex flex-col lg:flex-row gap-4 h-full">

        {/* Main Content Area (Video + Timeline) */}
        <div className="flex flex-col space-y-4 flex-1 min-w-0">
          {/* Video Stream - Responsive sizing */}
          <LiveStreamCard
            streamPath="http://localhost:8888/test_stream/index.m3u8"
            isRecording={false}
            operatingRoom={currentProject.operatingRoom}
            maxHeight="calc(50vh - 2rem)" // Adjusted for timeline space
            className="flex-shrink-0"
            cardClassName="gap-2 px-2 pb-2"
            controls={false}
            muted={false}
            volume={1}
          />

          {/* Event Creation Bar - Mobile/Portrait Mode */}
          <div className="px-2 lg:hidden">
            <EventCreationBar className="w-full" />
          </div>

          {/* Timeline Section - Responsive height */}
          <NFMTimelineComplete
            maxHeight="calc(40vh - 2rem)" // Adjusted for video space
            className="flex-1 min-h-0"
            cardClassName="gap-2 px-2 pb-2"
            onEventEdit={handleEventEdit}
          />
        </div>

        {/* Event Creation Sidebar - Desktop/Landscape Mode */}
        <div className="hidden lg:block lg:w-80 xl:w-96 flex-shrink-0">
          <div className="sticky top-4">
            <EventCreationBar className="w-full" />
          </div>
        </div>
      </div>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </VideoTimelineProvider>
  );
}
