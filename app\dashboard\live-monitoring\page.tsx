"use client";

import React, { useCallback, useState, useEffect } from "react";
import { LiveStreamCard } from "@/components/video/LiveStreamCard";
import { NFMTimelineComplete } from "@/components/timeline/NFMTimelineComplete";
import { EventCreationBar } from "@/components/events/EventCreationBar";
import { EventEditForm } from "@/components/events/EventEditForm";
import { useProjectContext } from "@/components/contexts/ProjectContext";
import { VideoTimelineProvider } from "@/components/contexts/VideoTimelineContext";

export default function LiveMonitoringPage() {
  console.log('[LiveMonitoringPage] RENDER - Component rendering')

  // Use project context for basic info
  const { currentProject, events, modalities } = useProjectContext();
  console.log('[LiveMonitoringPage] PROJECT CONTEXT - currentProject:', !!currentProject);

  // State for event editing and focus mode
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [focusMode, setFocusMode] = useState(false);
  const [isLandscape, setIsLandscape] = useState(true);

  // Detect aspect ratio for layout decisions
  useEffect(() => {
    const checkAspectRatio = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setIsLandscape(width > height);
    };

    checkAspectRatio();
    window.addEventListener('resize', checkAspectRatio);
    return () => window.removeEventListener('resize', checkAspectRatio);
  }, []);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // Memoize callback functions to prevent recreation and unnecessary re-renders
  const handleVideoSeek = useCallback((time: number) => {
    console.debug("[PAGE] Video seek to:", time);
  }, []);

  const handleTimelineSeek = useCallback((time: number) => {
    console.debug("[PAGE] Timeline seek to:", time);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    console.debug("[PAGE] Play state changed:", playing);
  }, []);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    console.debug("[PAGE] Playback rate changed:", rate);
  }, []);

  // Event editing handlers
  const handleEventEdit = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  const handleEditFormSave = useCallback((eventId: string) => {
    console.log('Event saved:', eventId);
    // Form will close automatically
  }, []);

  const handleEditFormDelete = useCallback((eventId: string) => {
    console.log('Event deleted:', eventId);
    // Form will close automatically
  }, []);

  console.log('[LiveMonitoringPage] CALLBACKS - Created callbacks:', {
    handleVideoSeek: !!handleVideoSeek,
    handleTimelineSeek: !!handleTimelineSeek,
    handlePlayStateChange: !!handlePlayStateChange,
    handlePlaybackRateChange: !!handlePlaybackRateChange,
    handleEventEdit: !!handleEventEdit
  })

  // Show loading state while data is loading
  if (!currentProject) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <VideoTimelineProvider
      initialDuration={3600} // 1 hour default
      onVideoSeek={handleVideoSeek}
      onTimelineSeek={handleTimelineSeek}
      onPlayStateChange={handlePlayStateChange}
      onPlaybackRateChange={handlePlaybackRateChange}
      events={events}
      modalities={modalities}
    >
      {/* Aspect Ratio-Based Layout - Allow Vertical Scrolling */}
      <div className="min-h-[calc(100vh-5.5rem)] flex flex-col overflow-y-auto">

        {isLandscape ? (
          /* Landscape Mode: Video + Sidebar, Timeline below */
          <div className="flex flex-col gap-4 h-full">
            {/* Top Row: Video + EventCreationBar side-by-side */}
            <div className="flex flex-row gap-4 flex-shrink-0">
              {/* Video Stream */}
              <div className="flex-1 min-w-0">
                <LiveStreamCard
                  streamPath="http://localhost:8888/test_stream/index.m3u8"
                  isRecording={false}
                  operatingRoom={currentProject.operatingRoom}
                  maxHeight={focusMode ? "75vh" : "calc(50vh - 2rem)"}
                  className="h-full"
                  cardClassName="gap-2 px-2 pb-2 h-full"
                  controls={false}
                  muted={false}
                  volume={1}
                  focusMode={focusMode}
                  onFocusModeToggle={setFocusMode}
                  maintainAspectRatio={true}
                  aspectRatio="16/9"
                />
              </div>

              {/* Event Creation Sidebar - Matches video height */}
              <div className="w-72 lg:w-80 xl:w-96 flex-shrink-0">
                <EventCreationBar
                  className="h-full"
                  focusMode={focusMode}
                  layout="vertical"
                />
              </div>
            </div>

            {/* Timeline Section - Full Width with minimum height */}
            <div className="w-full">
              <NFMTimelineComplete
                maxHeight={focusMode ? "calc(25vh - 1rem)" : "calc(40vh - 2rem)"}
                className="w-full"
                cardClassName="gap-2 px-2 pb-2"
                onEventEdit={handleEventEdit}
                showHeader={!focusMode}
                showControls={!focusMode}
              />
            </div>
          </div>
        ) : (
          /* Portrait Mode: Stacked layout with horizontal buttons */
          <div className="flex flex-col gap-4 h-full">
            {/* Video Stream */}
            <div className="flex-shrink-0">
              <LiveStreamCard
                streamPath="http://localhost:8888/test_stream/index.m3u8"
                isRecording={false}
                operatingRoom={currentProject.operatingRoom}
                maxHeight={focusMode ? "60vh" : "calc(40vh - 2rem)"}
                className="w-full"
                cardClassName="gap-2 px-2 pb-2"
                controls={false}
                muted={false}
                volume={1}
                focusMode={focusMode}
                onFocusModeToggle={setFocusMode}
                maintainAspectRatio={true}
                aspectRatio="16/9"
              />
            </div>

            {/* Event Creation Bar - Horizontal compact layout */}
            <div className="flex-shrink-0">
              <EventCreationBar
                className="w-full"
                focusMode={focusMode}
                layout="horizontal"
              />
            </div>

            {/* Timeline Section */}
            <div className="flex-1 min-h-0">
              <NFMTimelineComplete
                maxHeight={focusMode ? "calc(30vh - 1rem)" : "calc(50vh - 2rem)"}
                className="w-full h-full"
                cardClassName="gap-2 px-2 pb-2 h-full"
                onEventEdit={handleEventEdit}
                showHeader={!focusMode}
                showControls={!focusMode}
              />
            </div>
          </div>
        )}
      </div>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </VideoTimelineProvider>
  );
}
