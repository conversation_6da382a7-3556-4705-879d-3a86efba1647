"use client";

import React, { useCallback, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import { Plus, AlertTriangle, Activity, Zap, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { useEventCreation } from '@/hooks/useEventCreation';
import { EventEditForm } from './EventEditForm';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

interface EventCreationBarProps {
  className?: string;
  disabled?: boolean;
  showCustomButton?: boolean;
}

interface EventButtonConfig {
  modalityId: string;
  modalityName: string;
  eventType: string;
  label: string;
  severity: "normal" | "warning" | "critical";
  color: string;
  icon: React.ReactNode;
  description?: string;
}

export function EventCreationBar({
  className,
  disabled = false,
  showCustomButton = true
}: EventCreationBarProps) {
  // Get project context and video timeline state
  const { currentProject, modalities, currentUserId } = useProjectContext();
  const { currentTime } = useVideoTimeline();
  const { createEvent, isCreating, error, clearError } = useEventCreation();

  // Fetch all event types
  const eventTypes = useQuery(api.eventTypes.getAllEventTypes);

  // State for event type selection and edit form
  const [selectedEventTypeId, setSelectedEventTypeId] = useState<Id<"eventTypes"> | null>(null);
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // Generate event buttons based on enabled modalities
  const eventButtons = useMemo<EventButtonConfig[]>(() => {
    if (!modalities || modalities.length === 0) return [];

    const buttons: EventButtonConfig[] = [];

    modalities.forEach(modality => {
      // Skip the "ALL" modality for event creation
      if (modality.name === 'ALL') return;

      // Get the first event type for each modality as the primary button
      const primaryEventType = modality.eventTypes?.[0];
      if (!primaryEventType) return;

      // Determine icon based on modality name
      let icon: React.ReactNode;
      let defaultSeverity: "normal" | "warning" | "critical" = primaryEventType.severity;

      switch (modality.name.toUpperCase()) {
        case 'MEP':
          icon = <AlertTriangle className="h-4 w-4" />;
          defaultSeverity = 'critical';
          break;
        case 'EMG':
          icon = <Activity className="h-4 w-4" />;
          defaultSeverity = 'warning';
          break;
        case 'SSEP':
          icon = <Zap className="h-4 w-4" />;
          defaultSeverity = 'normal';
          break;
        default:
          icon = <Activity className="h-4 w-4" />;
          break;
      }

      buttons.push({
        modalityId: modality._id,
        modalityName: modality.name,
        eventType: primaryEventType.name,
        label: `${modality.name} ${primaryEventType.name}`,
        severity: defaultSeverity,
        color: modality.colorCode,
        icon,
        description: primaryEventType.description
      });
    });

    return buttons;
  }, [modalities]);

  // Handle edit event
  const handleEditEvent = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  // Handle edit form close
  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  // Handle edit form save
  const handleEditFormSave = useCallback((eventId: string) => {
    toast.success('Event updated successfully');
    // Form will close automatically
  }, []);

  // Handle edit form delete
  const handleEditFormDelete = useCallback((eventId: string) => {
    toast.success('Event deleted successfully');
    // Form will close automatically
  }, []);

  // Handle event creation with enhanced toast notifications
  const handleCreateEvent = useCallback(async (buttonConfig: EventButtonConfig, overrideSeverity?: "normal" | "warning" | "critical") => {
    if (!currentProject || !currentUserId) {
      toast.error('Project or user not available');
      return;
    }

    const finalSeverity = overrideSeverity || selectedSeverity;

    try {
      const eventId = await createEvent({
        modalityId: buttonConfig.modalityId as Id<"modalityConfigs">,
        eventType: buttonConfig.eventType,
        severity: finalSeverity,
        timestamp: currentTime,
        title: buttonConfig.label,
        description: buttonConfig.description || `${buttonConfig.modalityName} event created at ${Math.floor(currentTime)}s`
      });

      // Enhanced toast with edit button
      if (eventId) {
        toast.success(`${buttonConfig.label} created successfully`, {
          description: `Severity: ${finalSeverity} • Time: ${Math.floor(currentTime)}s`,
          action: {
            label: "Edit",
            onClick: () => handleEditEvent(eventId)
          },
          duration: 5000
        });
      } else {
        toast.success(`${buttonConfig.label} created successfully`);
      }
    } catch (error) {
      console.error('Failed to create event:', error);
      toast.error(`Failed to create ${buttonConfig.label}`);
    }
  }, [currentProject, currentUserId, currentTime, createEvent, selectedSeverity, handleEditEvent]);

  // Handle custom event creation
  const handleCreateCustomEvent = useCallback(() => {
    // For now, create a generic event with the first available modality
    const firstModality = modalities?.find(m => m.name !== 'ALL');
    if (!firstModality) {
      toast.error('No modalities available for custom event');
      return;
    }

    handleCreateEvent({
      modalityId: firstModality._id,
      modalityName: firstModality.name,
      eventType: 'Custom Event',
      label: 'Custom Event',
      severity: 'normal',
      color: firstModality.colorCode,
      icon: <Plus className="h-4 w-4" />,
      description: `Custom event created at ${Math.floor(currentTime)}s`
    });
  }, [modalities, currentTime, handleCreateEvent]);

  // Clear error when component unmounts or error changes
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(clearError, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Don't render if no project or modalities
  if (!currentProject || !modalities || modalities.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Error display */}
      {error && (
        <div className="p-2 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Severity selection and event creation controls */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
        {/* Severity selection */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Severity:</span>
          <Select value={selectedSeverity} onValueChange={(value) => setSelectedSeverity(value as "normal" | "warning" | "critical")}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="normal">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500" />
                  Normal
                </div>
              </SelectItem>
              <SelectItem value="warning">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-yellow-500" />
                  Warning
                </div>
              </SelectItem>
              <SelectItem value="critical">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-red-500" />
                  Critical
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Event creation buttons */}
        <div className="flex flex-wrap gap-2">
        {eventButtons.map((button) => (
          <Button
            key={`${button.modalityId}-${button.eventType}`}
            variant="outline"
            size="sm"
            disabled={disabled || isCreating}
            onClick={() => handleCreateEvent(button)}
            className={cn(
              "h-12 px-4 flex items-center gap-2 transition-all duration-200",
              "hover:scale-105 active:scale-95",
              "border-2",
              // Color coding based on severity
              button.severity === 'critical' && "border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300",
              button.severity === 'warning' && "border-yellow-200 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-300",
              button.severity === 'normal' && "border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300"
            )}
            title={button.description || `Create ${button.label} at ${Math.floor(currentTime)}s`}
          >
            {/* Modality color indicator */}
            <div 
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: button.color }}
            />
            {button.icon}
            <span className="font-medium">{button.modalityName}</span>
            <Badge 
              variant={
                button.severity === 'critical' ? 'destructive' :
                button.severity === 'warning' ? 'secondary' : 'default'
              }
              className="text-xs"
            >
              {button.severity}
            </Badge>
          </Button>
        ))}

        {/* Custom event button */}
        {showCustomButton && (
          <Button
            variant="outline"
            size="sm"
            disabled={disabled || isCreating}
            onClick={handleCreateCustomEvent}
            className={cn(
              "h-12 px-4 flex items-center gap-2 transition-all duration-200",
              "hover:scale-105 active:scale-95",
              "border-2 border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300"
            )}
            title={`Create custom event at ${Math.floor(currentTime)}s`}
          >
            <Plus className="h-4 w-4" />
            <span className="font-medium">Custom</span>
          </Button>
        )}
        </div>
      </div>

      {/* Current time indicator */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>Events will be created at current time: {Math.floor(currentTime)}s</span>
        {isCreating && (
          <Badge variant="secondary" className="animate-pulse">
            Creating...
          </Badge>
        )}
      </div>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </div>
  );
}
