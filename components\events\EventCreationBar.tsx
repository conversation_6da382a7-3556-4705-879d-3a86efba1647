"use client";

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu';

import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import { Plus, AlertTriangle, Activity, Zap, Settings, ChevronDown } from 'lucide-react';
import { toast } from 'sonner';
import { useEventCreation } from '@/hooks/useEventCreation';
import { EventEditForm } from './EventEditForm';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

interface EventCreationBarProps {
  className?: string;
  disabled?: boolean;
  showCustomButton?: boolean;
}

interface EventButtonConfig {
  modalityId: string;
  modalityName: string;
  eventTypeId: string;
  label: string;
  severity: "normal" | "warning" | "critical";
  color: string;
  icon: React.ReactNode;
  description?: string;
}

export function EventCreationBar({
  className,
  disabled = false,
  showCustomButton = true
}: EventCreationBarProps) {
  // Get project context and video timeline state
  const { currentProject, modalities, currentUserId } = useProjectContext();
  const { currentTime } = useVideoTimeline();
  const { createEvent, isCreating, error, clearError } = useEventCreation();

  // Fetch all event types with their modality information
  const eventTypes = useQuery(api.eventTypes.getAllEventTypes);

  // State for edit form
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // Generate default event buttons (one per modality showing default eventType)
  const defaultEventButtons = useMemo<EventButtonConfig[]>(() => {
    if (!eventTypes || !modalities || eventTypes.length === 0 || modalities.length === 0) return [];

    const buttons: EventButtonConfig[] = [];

    // Group eventTypes by modality
    const eventTypesByModality = eventTypes.reduce((acc, eventType) => {
      const modalityId = eventType.modalityId;
      if (!acc[modalityId]) {
        acc[modalityId] = [];
      }
      acc[modalityId].push(eventType);
      return acc;
    }, {} as Record<string, typeof eventTypes>);

    modalities.forEach(modality => {
      // Skip the "ALL" modality for event creation
      if (modality.name === 'ALL') return;

      const modalityEventTypes = eventTypesByModality[modality._id];
      if (!modalityEventTypes || modalityEventTypes.length === 0) return;

      // Get the default (first) event type for this modality
      const defaultEventType = modalityEventTypes[0];

      // Determine icon based on severity
      let icon: React.ReactNode;
      switch (defaultEventType.severity) {
        case 'critical':
          icon = <AlertTriangle className="h-4 w-4" />;
          break;
        case 'warning':
          icon = <Activity className="h-4 w-4" />;
          break;
        case 'normal':
          icon = <Zap className="h-4 w-4" />;
          break;
        default:
          icon = <Plus className="h-4 w-4" />;
      }

      buttons.push({
        modalityId: modality._id,
        modalityName: modality.name,
        eventTypeId: defaultEventType._id,
        label: defaultEventType.name, // Show eventType name, not modality name
        severity: defaultEventType.severity,
        color: modality.colorCode,
        icon,
        description: defaultEventType.description
      });
    });

    return buttons;
  }, [eventTypes, modalities]);

  // Group all eventTypes by modality for context menus
  const eventTypesByModality = useMemo(() => {
    if (!eventTypes) return {};

    return eventTypes.reduce((acc, eventType) => {
      const modalityId = eventType.modalityId;
      if (!acc[modalityId]) {
        acc[modalityId] = [];
      }
      acc[modalityId].push(eventType);
      return acc;
    }, {} as Record<string, typeof eventTypes>);
  }, [eventTypes]);

  // Handle edit event
  const handleEditEvent = useCallback((eventId: string) => {
    setEditingEventId(eventId);
    setIsEditFormOpen(true);
  }, []);

  // Handle edit form close
  const handleEditFormClose = useCallback(() => {
    setIsEditFormOpen(false);
    setEditingEventId(null);
  }, []);

  // Handle edit form save
  const handleEditFormSave = useCallback((_eventId: string) => {
    toast.success('Event updated successfully');
    // Form will close automatically
  }, []);

  // Handle edit form delete
  const handleEditFormDelete = useCallback((_eventId: string) => {
    toast.success('Event deleted successfully');
    // Form will close automatically
  }, []);

  // Handle event creation with enhanced toast notifications
  const handleCreateEvent = useCallback(async (buttonConfig: EventButtonConfig, overrideSeverity?: "normal" | "warning" | "critical") => {
    if (!currentProject || !currentUserId) {
      toast.error('Project or user not available');
      return;
    }

    try {
      const eventId = await createEvent({
        modalityId: buttonConfig.modalityId as Id<"modalityConfigs">,
        eventTypeId: buttonConfig.eventTypeId as Id<"eventTypes">,
        startTime: currentTime,
        title: buttonConfig.label,
        description: buttonConfig.description || `${buttonConfig.modalityName} event created at ${Math.floor(currentTime)}s`
      });

      // Enhanced toast with edit button
      if (eventId) {
        toast.success(`${buttonConfig.label} created successfully`, {
          description: `Severity: ${buttonConfig.severity} • Time: ${Math.floor(currentTime)}s`,
          action: {
            label: "Edit",
            onClick: () => handleEditEvent(eventId)
          },
          duration: 5000
        });
      } else {
        toast.success(`${buttonConfig.label} created successfully`);
      }
    } catch (error) {
      console.error('Failed to create event:', error);
      toast.error(`Failed to create ${buttonConfig.label}`);
    }
  }, [currentProject, currentUserId, currentTime, createEvent, handleEditEvent]);

  // Handle custom event creation
  const handleCreateCustomEvent = useCallback(() => {
    // For now, create a generic event with the first available modality
    const firstModality = modalities?.find(m => m.name !== 'ALL');
    if (!firstModality) {
      toast.error('No modalities available for custom event');
      return;
    }

    // For custom events, we need to find a default eventType
    // This is a temporary solution - ideally we'd have a proper custom event creation dialog
    const firstEventType = eventTypes?.[0];
    if (!firstEventType) {
      toast.error('No event types available');
      return;
    }

    handleCreateEvent({
      modalityId: firstModality._id,
      modalityName: firstModality.name,
      eventTypeId: firstEventType._id,
      label: 'Custom Event',
      severity: firstEventType.severity,
      color: firstModality.colorCode,
      icon: <Plus className="h-4 w-4" />,
      description: `Custom event created at ${Math.floor(currentTime)}s`
    });
  }, [modalities, currentTime, handleCreateEvent]);

  // Clear error when component unmounts or error changes
  React.useEffect(() => {
    if (error) {
      const timer = setTimeout(clearError, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Don't render if no project or modalities
  if (!currentProject || !modalities || modalities.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Error display */}
      {error && (
        <div className="p-2 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Event creation controls */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">

        {/* Default Event Type Buttons with Context Menus */}
        <div className="flex flex-wrap gap-2">
        {defaultEventButtons.map((button) => {
          const modalityEventTypes = eventTypesByModality[button.modalityId] || [];

          return (
            <DropdownMenu key={`${button.modalityId}-${button.eventTypeId}`}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={disabled || isCreating}
                  onClick={(e) => {
                    // Left click creates default eventType directly
                    if (e.detail === 1) { // Single click
                      handleCreateEvent(button);
                    }
                  }}
                  onContextMenu={(e) => {
                    e.preventDefault(); // Prevent browser context menu
                  }}
                  className={cn(
                    "h-12 px-4 flex items-center gap-2 transition-all duration-200",
                    "hover:scale-105 active:scale-95",
                    "border-2 relative",
                    // Color coding based on default eventType severity
                    button.severity === 'critical' && "border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300",
                    button.severity === 'warning' && "border-yellow-200 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-300",
                    button.severity === 'normal' && "border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300"
                  )}
                  title={`${button.label} (${button.modalityName}) - Click for default, right-click for options`}
                >
                  {/* Modality color indicator */}
                  <div
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: button.color }}
                  />
                  {button.icon}
                  <div className="flex flex-col items-start">
                    <span className="font-medium text-sm">{button.label}</span>
                    <span className="text-xs text-muted-foreground">{button.modalityName}</span>
                  </div>
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="start" className="w-56">
                <DropdownMenuLabel className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: button.color }}
                  />
                  {button.modalityName} Events
                </DropdownMenuLabel>
                <DropdownMenuSeparator />

                {modalityEventTypes.map((eventType) => {
                  const getSeverityIcon = (severity: string) => {
                    switch (severity) {
                      case 'critical': return AlertTriangle;
                      case 'warning': return Activity;
                      case 'normal': return Zap;
                      default: return Plus;
                    }
                  };

                  const SeverityIcon = getSeverityIcon(eventType.severity);

                  return (
                    <DropdownMenuItem
                      key={eventType._id}
                      onClick={() => handleCreateEvent({
                        modalityId: button.modalityId,
                        modalityName: button.modalityName,
                        eventTypeId: eventType._id,
                        label: eventType.name,
                        severity: eventType.severity,
                        color: button.color,
                        icon: <SeverityIcon className="h-4 w-4" />,
                        description: eventType.description
                      })}
                      className="flex items-center gap-3 cursor-pointer"
                    >
                      <SeverityIcon className={cn(
                        "h-4 w-4",
                        eventType.severity === 'critical' && "text-red-600",
                        eventType.severity === 'warning' && "text-yellow-600",
                        eventType.severity === 'normal' && "text-green-600"
                      )} />
                      <div className="flex-1">
                        <div className="font-medium">{eventType.name}</div>
                        {eventType.description && (
                          <div className="text-xs text-muted-foreground">{eventType.description}</div>
                        )}
                      </div>
                      <Badge
                        variant="secondary"
                        className={cn(
                          "text-xs",
                          eventType.severity === 'critical' && "bg-red-100 text-red-700",
                          eventType.severity === 'warning' && "bg-yellow-100 text-yellow-700",
                          eventType.severity === 'normal' && "bg-green-100 text-green-700"
                        )}
                      >
                        {eventType.severity}
                      </Badge>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        })}

        {/* Custom event button */}
        {showCustomButton && (
          <Button
            variant="outline"
            size="sm"
            disabled={disabled || isCreating}
            onClick={handleCreateCustomEvent}
            className={cn(
              "h-12 px-4 flex items-center gap-2 transition-all duration-200",
              "hover:scale-105 active:scale-95",
              "border-2 border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300"
            )}
            title={`Create custom event at ${Math.floor(currentTime)}s`}
          >
            <Plus className="h-4 w-4" />
            <span className="font-medium">Custom</span>
          </Button>
        )}
        </div>
      </div>

      {/* Current time indicator */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>Events will be created at current time: {Math.floor(currentTime)}s</span>
        {isCreating && (
          <Badge variant="secondary" className="animate-pulse">
            Creating...
          </Badge>
        )}
      </div>

      {/* Event Edit Form */}
      <EventEditForm
        eventId={editingEventId}
        isOpen={isEditFormOpen}
        onClose={handleEditFormClose}
        onSave={handleEditFormSave}
        onDelete={handleEditFormDelete}
      />
    </div>
  );
}
