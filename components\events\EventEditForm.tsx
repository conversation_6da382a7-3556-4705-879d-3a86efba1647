"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { toast } from 'sonner';
import { Save, X, Trash2, Clock, User, AlertTriangle } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface EventEditFormProps {
  eventId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (eventId: string) => void;
  onDelete?: (eventId: string) => void;
}

interface EventFormData {
  title: string;
  description: string;
  severity: "normal" | "warning" | "critical";
  modalityId: Id<"modalityConfigs">;
  eventType: string;
  startTime: number;
  endTime?: number;
  location?: string;
}

export function EventEditForm({
  eventId,
  isOpen,
  onClose,
  onSave,
  onDelete
}: EventEditFormProps) {
  const {currentProjectId, modalities } = useProjectContext();
  
  // State for form data
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    severity: 'normal',
    modalityId: '' as Id<"modalityConfigs">,
    eventType: '',
    startTime: 0,
    endTime: undefined,
    location: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch single event data
  const eventData = useQuery(
    api.timeline.getMonitoringEvent,
    eventId ? { eventId: eventId as Id<"monitoringEvents"> } : "skip"
  );

  // Mutations
  const updateEvent = useMutation(api.timeline.updateMonitoringEvent);
  const deleteEvent = useMutation(api.timeline.deleteMonitoringEvent);

  // Load event data when eventId changes
  useEffect(() => {
    if (eventId && eventData) {
      setFormData({
        title: eventData.title || '',
        description: eventData.description || '',
        severity: eventData.severity,
        modalityId: eventData.modalityId,
        eventType: eventData.eventType || '',
        startTime: eventData.startTime,
        endTime: eventData.endTime,
        location: eventData.location || ''
      });
      setHasChanges(false);
    }
  }, [eventId, eventData]);

  // Handle form field changes
  const handleFieldChange = useCallback((field: keyof EventFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!eventId) return;

    setIsLoading(true);
    try {
      await updateEvent({
        eventId: eventId as Id<"monitoringEvents">,
        title: formData.title,
        description: formData.description,
        severity: formData.severity,
        modalityId: formData.modalityId,
        eventType: formData.eventType,
        startTime: formData.startTime,
        endTime: formData.endTime
      });

      toast.success('Event updated successfully');
      setHasChanges(false);
      onSave?.(eventId);
    } catch (error) {
      console.error('Failed to update event:', error);
      toast.error('Failed to update event');
    } finally {
      setIsLoading(false);
    }
  }, [eventId, formData, updateEvent, onSave]);

  // Handle delete
  const handleDelete = useCallback(async () => {
    if (!eventId) return;

    if (!confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    try {
      await deleteEvent({ eventId: eventId as Id<"monitoringEvents"> });
      toast.success('Event deleted successfully');
      onDelete?.(eventId);
      onClose();
    } catch (error) {
      console.error('Failed to delete event:', error);
      toast.error('Failed to delete event');
    } finally {
      setIsLoading(false);
    }
  }, [eventId, deleteEvent, onDelete, onClose]);

  // Handle close with unsaved changes warning
  const handleClose = useCallback(() => {
    if (hasChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to close?')) {
        onClose();
      }
    } else {
      onClose();
    }
  }, [hasChanges, onClose]);

  // Get current modality info
  const currentModality = modalities?.find(m => m._id === formData.modalityId);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Edit Event
            {hasChanges && (
              <Badge variant="secondary" className="text-xs">
                Unsaved Changes
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Event Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Start: {Math.floor(formData.startTime)}s</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>ID: {eventId?.slice(-8)}</span>
            </div>
          </div>

          <Separator />

          {/* Form Fields */}
          <div className="space-y-4">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                placeholder="Event title"
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Event description"
                rows={3}
              />
            </div>

            {/* Severity and Modality */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="severity">Severity</Label>
                <Select
                  value={formData.severity}
                  onValueChange={(value) => handleFieldChange('severity', value as "normal" | "warning" | "critical")}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="normal">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        Normal
                      </div>
                    </SelectItem>
                    <SelectItem value="warning">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-500" />
                        Warning
                      </div>
                    </SelectItem>
                    <SelectItem value="critical">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500" />
                        Critical
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modality">Modality</Label>
                <div className="flex items-center gap-2">
                  {currentModality && (
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: currentModality.colorCode }}
                    />
                  )}
                  <span className="text-sm font-medium">
                    {currentModality?.displayName || 'Unknown'}
                  </span>
                </div>
              </div>
            </div>

            {/* Event Type and Location */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="eventType">Event Type</Label>
                <Input
                  id="eventType"
                  value={formData.eventType}
                  onChange={(e) => handleFieldChange('eventType', e.target.value)}
                  placeholder="Event type"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location || ''}
                  onChange={(e) => handleFieldChange('location', e.target.value)}
                  placeholder="Anatomical location"
                />
              </div>
            </div>

            {/* Timing */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime">Start Time (seconds)</Label>
                <Input
                  id="startTime"
                  type="number"
                  value={formData.startTime}
                  onChange={(e) => handleFieldChange('startTime', parseFloat(e.target.value) || 0)}
                  step="0.1"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">End Time (seconds)</Label>
                <Input
                  id="endTime"
                  type="number"
                  value={formData.endTime || ''}
                  onChange={(e) => handleFieldChange('endTime', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="Optional"
                  step="0.1"
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={isLoading}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !hasChanges}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
