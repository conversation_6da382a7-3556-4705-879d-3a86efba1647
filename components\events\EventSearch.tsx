"use client";

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import { Search, X, Clock, MapPin, AlertTriangle, Activity, Zap } from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';

interface SearchResult {
  eventId: Id<"monitoringEvents">;
  title: string;
  description: string;
  severity: "normal" | "warning" | "critical";
  modalityName: string;
  modalityColor: string;
  startTime: number;
  endTime?: number;
  location?: string;
  matchType: 'title' | 'description' | 'both';
  matchText: string;
}

interface EventSearchProps {
  onEventSelect?: (eventId: string, time: number) => void;
  className?: string;
}

export function EventSearch({
  onEventSelect,
  className
}: EventSearchProps) {
  const { events, modalities } = useProjectContext();
  const { seekTo } = useVideoTimeline();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Search results
  const searchResults = useMemo<SearchResult[]>(() => {
    if (!searchQuery.trim() || !events || !modalities) return [];

    const query = searchQuery.toLowerCase().trim();
    const results: SearchResult[] = [];

    events.forEach(event => {
      const modality = modalities.find(m => m._id === event.modalityId);
      if (!modality) return;

      const titleMatch = event.title?.toLowerCase().includes(query);
      const descriptionMatch = event.description?.toLowerCase().includes(query);
      const locationMatch = event.location?.toLowerCase().includes(query);

      if (titleMatch || descriptionMatch || locationMatch) {
        let matchType: 'title' | 'description' | 'both' = 'title';
        let matchText = '';

        if (titleMatch && descriptionMatch) {
          matchType = 'both';
          matchText = `${event.title} • ${event.description}`;
        } else if (titleMatch) {
          matchType = 'title';
          matchText = event.title || '';
        } else if (descriptionMatch) {
          matchType = 'description';
          matchText = event.description || '';
        } else if (locationMatch) {
          matchType = 'description';
          matchText = event.location || '';
        }

        results.push({
          eventId: event._id,
          title: event.title || 'Untitled Event',
          description: event.description || '',
          severity: event.severity,
          modalityName: modality.displayName,
          modalityColor: modality.colorCode,
          startTime: event.startTime,
          endTime: event.endTime,
          location: event.location,
          matchType,
          matchText
        });
      }
    });

    // Sort by relevance (title matches first, then by time)
    return results.sort((a, b) => {
      if (a.matchType === 'title' && b.matchType !== 'title') return -1;
      if (b.matchType === 'title' && a.matchType !== 'title') return 1;
      return a.startTime - b.startTime;
    });
  }, [searchQuery, events, modalities]);

  // Handle search input change
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    setSelectedIndex(0);
    if (value.trim()) {
      setIsOpen(true);
    }
  }, []);

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    seekTo(result.startTime);
    onEventSelect?.(result.eventId, result.startTime);
    setIsOpen(false);
    setSearchQuery('');
  }, [seekTo, onEventSelect]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen || searchResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, searchResults.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (searchResults[selectedIndex]) {
          handleResultSelect(searchResults[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        break;
    }
  }, [isOpen, searchResults, selectedIndex, handleResultSelect]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setIsOpen(false);
    setSelectedIndex(0);
  }, []);

  // Get severity info
  const getSeverityInfo = (severity: "normal" | "warning" | "critical") => {
    switch (severity) {
      case 'critical':
        return { icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-100' };
      case 'warning':
        return { icon: Activity, color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
      case 'normal':
        return { icon: Zap, color: 'text-green-600', bgColor: 'bg-green-100' };
    }
  };

  // Highlight search terms in text
  const highlightText = useCallback((text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-0.5 rounded">
          {part}
        </mark>
      ) : part
    );
  }, []);

  // Format time display
  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return (
    <div className={cn("relative", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search events..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => searchQuery.trim() && setIsOpen(true)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </PopoverTrigger>
        
        {searchResults.length > 0 && (
          <PopoverContent className="w-96 p-0" align="start">
            <div className="p-3 border-b">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
                </span>
                <span className="text-xs text-muted-foreground">
                  Use ↑↓ to navigate, Enter to select
                </span>
              </div>
            </div>
            
            <ScrollArea className="max-h-80">
              <div className="p-1">
                {searchResults.map((result, index) => {
                  const { icon: SeverityIcon, color, bgColor } = getSeverityInfo(result.severity);
                  const isSelected = index === selectedIndex;
                  
                  return (
                    <div
                      key={result.eventId}
                      className={cn(
                        "p-3 rounded-md cursor-pointer transition-colors",
                        isSelected ? "bg-accent" : "hover:bg-accent/50"
                      )}
                      onClick={() => handleResultSelect(result)}
                    >
                      <div className="flex items-start gap-3">
                        {/* Modality indicator */}
                        <div
                          className="w-3 h-3 rounded-full mt-1 flex-shrink-0"
                          style={{ backgroundColor: result.modalityColor }}
                        />
                        
                        <div className="flex-1 min-w-0">
                          {/* Title and severity */}
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm truncate">
                              {highlightText(result.title, searchQuery)}
                            </h4>
                            <div className={cn("p-0.5 rounded", bgColor)}>
                              <SeverityIcon className={cn("h-3 w-3", color)} />
                            </div>
                          </div>
                          
                          {/* Description */}
                          {result.description && (
                            <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                              {highlightText(result.description, searchQuery)}
                            </p>
                          )}
                          
                          {/* Metadata */}
                          <div className="flex items-center gap-3 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTime(result.startTime)}
                            </div>
                            <span>{result.modalityName}</span>
                            {result.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {result.location}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </PopoverContent>
        )}
        
        {searchQuery.trim() && searchResults.length === 0 && (
          <PopoverContent className="w-96" align="start">
            <div className="text-center py-6">
              <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                No events found for "{searchQuery}"
              </p>
            </div>
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
}
