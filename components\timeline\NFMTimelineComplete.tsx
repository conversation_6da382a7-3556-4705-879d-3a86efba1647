"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef, useMemo } from "react"
import { NFMTimelineEditor, NFMTimelineControls, TimelineEvent, TimelineState } from "@/components/timeline"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu"
import { Activity, Settings, Download, Upload, Keyboard, Save } from "lucide-react"
import { cn } from "@/lib/utils"
import { Doc, Id } from "@/convex/_generated/dataModel"
import { useProjectContext } from "../contexts/ProjectContext"
import { useVideoTimeline } from "../contexts/VideoTimelineContext"

import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { toast } from "sonner"
import { useIsMobile } from "@/hooks/use-mobile"
import { TimelineKeyboardShortcutsHelp } from "./NFMTimelineKeyboardShortcuts"
import { InteractionMode } from "@/types/timelineEditor"
import { formatDuration } from "@/utils/mediaHelpers"
import { parserTimeToPixel } from "./utils/deal_data"
import { DEFAULT_SCALE_WIDTH, DEFAULT_START_LEFT } from "./interface/const"


interface NFMTimelineCompleteProps {
  // Configuration
  showControls?: boolean
  showHeader?: boolean

  // Layout
  className?: string
  cardClassName?: string
  timelineHeight?: string
  maxHeight?: string

  // Event handlers
  onEventAdd?: (event: Partial<TimelineEvent>) => void
  onEventUpdate?: (eventId: string, updates: Partial<TimelineEvent>) => void
  onEventDelete?: (eventId: string) => void
  onModalityVisibilityChange?: (modalityIds: string[]) => void

  // Mobile optimization
  isMobile?: boolean

  // Legacy props (deprecated - now handled by VideoTimelineContext)
  //duration?: number
  //currentTime?: number
  //isPlaying?: boolean
  //onTimeChange?: (time: number) => void
 // onPlayStateChange?: (isPlaying: boolean) => void
}
const NFMTimelineCompleteComponent = function NFMTimelineComplete({
  showControls = true,
  showHeader = true,
  className,
  cardClassName,
  // Legacy props - now handled by VideoTimelineContext
  //duration,
  //currentTime,
  //isPlaying,
  //onTimeChange,
  //onPlayStateChange,
}: NFMTimelineCompleteProps) {
  console.log('[NFMTimelineComplete] RENDER - Component rendering with props:', {
    showControls,
    showHeader,
    className: !!className,
    cardClassName: !!cardClassName
  })



  const isMobile = useIsMobile();
  // Use unified video-timeline state management
  const videoTimeline = useVideoTimeline()

  // Timeline state with VideoTimelineContext integration
  const [timelineEngine, setTimelineEngine] = useState<TimelineState | null>(null)

  // Use VideoTimelineContext for timeline scale management
  const safeTimelineScale = useMemo(() => {
    const scale = Math.max(10, Math.min(500, videoTimeline.timelineScale || 100))
    if (scale !== videoTimeline.timelineScale && videoTimeline.timelineScale !== 0) {
      console.warn('[NFMTimelineComplete] Invalid timeline scale detected, using safe value:', scale)
    }
    return scale
  }, [videoTimeline.timelineScale])

  // Callback to receive timeline engine from NFMTimelineEditor
  const handleTimelineEngineReady = useCallback((engine: TimelineState) => {
    console.log('[NFMTimelineComplete] TIMELINE ENGINE - Engine ready:', !!engine)
    setTimelineEngine(engine)
  }, [])

  
  // Memoize timeline engine event handlers to prevent recreation
  const handlePlay = useCallback(() => {
    console.log('[NFMTimelineComplete] HANDLER PLAY - Called')
    if (!videoTimeline.isPlaying) {
      videoTimeline.play();
    }
  }, [videoTimeline]);

  const handlePause = useCallback(() => {
    console.log('[NFMTimelineComplete] HANDLER PAUSE - Called')
    if (videoTimeline.isPlaying) {
      videoTimeline.pause();
    }
  }, [videoTimeline.isPlaying, videoTimeline.pause]);

  const handleTimeChange = useCallback(({ time }: { time: number }) => {
    console.log('[NFMTimelineComplete] HANDLER TIME CHANGE - Time:', time)
    // Only update current time, don't trigger video seek to prevent infinite loop
    videoTimeline.updateCurrentTime(time, 'timeline');
  }, [videoTimeline]);



  const handleTimeChangeByTick = useCallback(({ time }: { time: number }) => {
    console.log('[NFMTimelineComplete] HANDLER TIME TICK - Time:', time)
    // This is called during playback - update current time directly
    videoTimeline.updateCurrentTime(time, 'timeline');
    const autoScrollWhenPlay = true;
    if (autoScrollWhenPlay && timelineEngine) {
      const autoScrollFrom = 500;
      const left = time * (DEFAULT_SCALE_WIDTH / safeTimelineScale) + DEFAULT_START_LEFT - autoScrollFrom;
      timelineEngine.setScrollLeft(left);
    }
  }, [videoTimeline, safeTimelineScale, timelineEngine]);

  const handlePlaybackRateChange = useCallback(({ rate }: { rate: number }) => {
    console.log('[NFMTimelineComplete] HANDLER PLAYBACK RATE - Rate:', rate)
    videoTimeline.setPlaybackRate(rate);
  }, [videoTimeline]);





  // Initialize timeline engine listeners with proper cleanup
  useEffect(() => {
    console.log('[NFMTimelineComplete] TIMELINE LISTENERS - Setting up listeners')
    if (!timelineEngine) return;

    timelineEngine.listener.on('play', handlePlay);
    timelineEngine.listener.on('paused', handlePause);
    timelineEngine.listener.on('afterSetTime', handleTimeChange);
    timelineEngine.listener.on('setTimeByTick', handleTimeChangeByTick);
    timelineEngine.listener.on('afterSetPlayRate', handlePlaybackRateChange);

    // Clean up listeners when dependencies change or component unmounts
    return () => {
      console.log('[NFMTimelineComplete] TIMELINE LISTENERS - Cleaning up listeners')
      timelineEngine.listener.off('play', handlePlay);
      timelineEngine.listener.off('paused', handlePause);
      timelineEngine.listener.off('afterSetTime', handleTimeChange);
      timelineEngine.listener.off('setTimeByTick', handleTimeChangeByTick);
      timelineEngine.listener.off('afterSetPlayRate', handlePlaybackRateChange);
    };
  }, [timelineEngine, handlePlay, handlePause, handleTimeChange, handleTimeChangeByTick, handlePlaybackRateChange]);


  // TODO Session management state
  const [isRecordingSession, setIsRecordingSession] = useState(false)
  const [sessionDuration, setSessionDuration] = useState("00:00:00")
  const [sessionTime, setSessionTime] = useState(0)

  // UI state
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)
  const [interactionMode, setInteractionModeState] = useState<InteractionMode>(InteractionMode.VIEW);


  // Database states
  const { currentProject, currentProjectId, currentSessionId, currentUserId } = useProjectContext();
  const projectId = currentProjectId!;
  // Fetch data directly with Convex queries
  const modalities = useQuery(api.timeline.getProjectModalities, projectId ? { projectId } : "skip");
  const events = useQuery(api.timeline.getProjectEvents, projectId ? {
    projectId,
    visibleModalitiesOnly: true
  } : "skip");

  // CONVEX Database Mutations
  const createEvent = useMutation(api.timeline.createMonitoringEvent);
  const deleteEvent = useMutation(api.timeline.deleteMonitoringEvent);
  const getOrCreateSession = useMutation(api.streamSessions.getOrCreateActiveSession);




  // TODO Session timer effect
  useEffect(() => {
    if (!isRecordingSession) return;

    const interval = setInterval(() => {
      const newTime = videoTimeline.currentTime;
      setSessionDuration(formatDuration(newTime));
      setSessionTime(newTime);
    }, 1000);

    return () => clearInterval(interval);
  }, [videoTimeline, isRecordingSession]);


  ////////////////////////// Session management handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  const handleStartRecording = async () => {
    if (!projectId) {
      toast.error("No project selected");
      return;
    }

    try {
      // Get or create an active session
      await getOrCreateSession({ projectId });
      setIsRecordingSession(true);
      setSessionTime(0);
      toast.success("Recording session started");
    } catch (error) {
      console.error("Failed to start session:", error);
      toast.error("Failed to start session");
    }
  };

  const handleStopRecording = () => {
    setIsRecordingSession(false);
    toast.info("Recording session stopped");
  };

  ////////////////////////// Event controls handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  // Use VideoTimelineContext for unified event navigation
  const handlePrevEvent = useCallback(() => {
    const success = videoTimeline.goToPrevEvent();
    if (!success) {
      console.debug('[NFMTimelineComplete] No previous event found');
    }
  }, [videoTimeline]);

  const handleNextEvent = useCallback(() => {
    const success = videoTimeline.goToNextEvent();
    if (!success) {
      console.debug('[NFMTimelineComplete] No next event found');
    }
  }, [videoTimeline]);

  const handleEventClick = (event: TimelineEvent | Doc<"monitoringEvents">) => {
    const startTime = 'start' in event ? event.start : event.startTime;
    const endTime = 'end' in event ? event.end : event.endTime;
    const timeRange = endTime ?
      `${Math.floor(startTime / 60)}:${(startTime % 60).toString().padStart(2, '0')} - ${Math.floor(endTime / 60)}:${(endTime % 60).toString().padStart(2, '0')}` :
      `${Math.floor(startTime / 60)}:${(startTime % 60).toString().padStart(2, '0')}`;

    toast.info(`Event: ${event.title} - ${timeRange}`);
  };

  // Note: Event creation is now handled automatically by the useTimelineData hook in NFMTimelineEditor

  const handleEventDelete = async (eventId: string) => {
    try {
      await deleteEvent({ eventId: eventId as Id<"monitoringEvents"> });
      toast.success("Event deleted successfully");
    } catch (error) {
      console.error("Failed to delete event:", error);
      toast.error("Failed to delete event");
    }
  }


  ////////////////////////// Timeline controls handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  /* const handleZoomOut = useCallback((value: number = 1) => {
     setTimelineScale(prev => Math.min(prev * 1.2, 500)) // Zoom OUT = higher scale = less magnification because px/sec is inversely proportional to scale
   }, [])*/

  const handleZoom = useCallback((value: number = 0.83) => {
    const newScale = Math.min(Math.max(videoTimeline.timelineScale * value, 10), 500);
    videoTimeline.setTimelineScale(newScale);
  }, [videoTimeline])

  const handleFitToView = useCallback(() => {
    // Enhanced fit-to-view: Calculate scale to fit all events
    if (events && events.length > 0) {
      const maxTime = Math.max(...events.map(event => event.endTime || event.startTime + 1));
      if (maxTime > 0) {
        // Assuming timeline width of 800px and scale relationship
        const newScale = Math.max(10, Math.min(500, (800 / maxTime) * 100));
        videoTimeline.setTimelineScale(newScale);
      }
    } else {
      videoTimeline.setTimelineScale(100); // Default scale
    }
  }, [events, videoTimeline])

  const handleReset = useCallback(() => {
    console.debug("[NFMTimelineComplete] Resetting timeline to default state");
    videoTimeline.setTimelineScale(100);
    videoTimeline.goToTime(0);
  }, [videoTimeline])

  // Handle auto-scroll to time for event navigation using VideoTimelineContext
  const handleScrollToTime = useCallback((time: number) => {
    console.debug(`[NFMTimelineComplete] Scrolling to time: ${time}`);

    // Use VideoTimelineContext safe seeking method
    videoTimeline.goToTime(time);

    // Calculate scroll position to center the time in viewport
    if (timelineEngine) {
      const targetPixelX = parserTimeToPixel(time, {
          startLeft: 10,
          scale: safeTimelineScale,
          scaleWidth: 800,
      });

      // Assume viewport width (this could be passed as a prop or calculated)
      const viewportWidth = 800; // Default viewport width
      const centeredScrollLeft = targetPixelX - viewportWidth / 2;
      const clampedScrollLeft = Math.max(0, centeredScrollLeft);

      // Access the timeline ref through the NFMTimelineEditor
      if (timelineEngine.setScrollLeft) {
        timelineEngine.setScrollLeft(clampedScrollLeft);
      }
    }
  }, [safeTimelineScale, timelineEngine, videoTimeline]);


  

  // Show loading state while data is loading
  if (!currentProject || !modalities || !events) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  // Backend already filters events by visibility, modalities have isVisible property
  const visibleModalities = modalities.filter(m => m.isVisible);

  // Calculate timeline height based on visible modalities (desktop auto-sizing)
  //const calculatedTimelineHeight = Math.max(200, Math.min(600, visibleModalities.length * 40 + 60)); // 40px per row + 60px for controls
  //const finalTimelineHeight = `${calculatedTimelineHeight}px`;
  

  return (
    <div className={cn("space-y-4", className)}>
      <Card className={cardClassName}>
        {showHeader && (
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Neuromonitoring Timeline
              </CardTitle>

              <div className="flex items-center gap-4">
                {/* Events and Modalities Stats */}
                <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex items-center gap-1">
                    <span className="text-xs">Events:</span>
                    <span className="font-semibold">{events?.length || 0}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs">Modalities:</span>
                    <span className="font-semibold">{modalities?.length || 0}</span>
                  </div>
                </div>

                {/* Options Button with Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      title="Timeline Options"
                      className="h-8 px-3"
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Options
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => setShowKeyboardHelp(true)}>
                      <Keyboard className="h-4 w-4 mr-2" />
                      Show Keyboard Info
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {/* TODO: Export functionality */ }}>
                      <Download className="h-4 w-4 mr-2" />
                      Export Timeline
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {/* TODO: Import functionality */ }}>
                      <Upload className="h-4 w-4 mr-2" />
                      Import Timeline
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {/* TODO: Save functionality */ }}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Session
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardHeader>
        )}

        <CardContent className={cn("space-y-4", !showHeader && "pt-6")}>
          {/* Timeline Controls */}
          {showControls && (
            <NFMTimelineControls
 
              events={events}
              modalities={modalities}
              //onPlay={videoTimeline.play}
              //onPause={videoTimeline.pause}
              //onPrevEvent={handlePrevEvent}
              //onNextEvent={handleNextEvent}
              //onZoom={handleZoom}
              //playbackRate={videoTimeline.playbackRate}
              //onPlaybackRateChange={videoTimeline.setPlaybackRate}
              //onScrollToTime={handleScrollToTime}
              interactionMode={interactionMode}
              setInteractionMode={setInteractionModeState}
              className=""
            />
          )}

          {/* Timeline Editor */}
          <div
            className="relative"
            style={{
              //height: calculatedTimelineHeight,
            }}
          >
            {/* Comprehensive safety check to prevent react-virtualized crashes */}
            {(!events || !Array.isArray(events) || !modalities || !Array.isArray(modalities) || safeTimelineScale <= 0 || !isFinite(safeTimelineScale)) ? (
              <div className="flex items-center justify-center h-64 text-muted-foreground">
                <div className="text-center">
                  <p>Loading timeline...</p>
                  {safeTimelineScale <= 0 && <p className="text-sm text-red-500">Invalid timeline scale: {safeTimelineScale}</p>}
                  {!isFinite(safeTimelineScale) && <p className="text-sm text-red-500">Timeline scale is not finite</p>}
                  {!events && <p className="text-sm text-red-500">No events data</p>}
                  {!Array.isArray(events) && <p className="text-sm text-red-500">Events data is not an array</p>}
                  {!modalities && <p className="text-sm text-red-500">No modalities data</p>}
                  {!Array.isArray(modalities) && <p className="text-sm text-red-500">Modalities data is not an array</p>}
                </div>
              </div>
            ) : (
              <NFMTimelineEditor
                onTimelineEngineReady={handleTimelineEngineReady}
                events={events}
                modalities={modalities}
                visibleModalities={visibleModalities}
                //currentTime={videoTimeline.currentTime}
                duration={videoTimeline.duration}
                isPlaying={videoTimeline.isPlaying}
                scale={safeTimelineScale}
                onScaleChange={handleZoom}
                //height={calculatedTimelineHeight}
                interactionMode={interactionMode}
              // onTimeChange={onTimeChange}
              // onPlayStateChange={onPlayStateChange}
              // onPlaybackRateChange={setPlaybackRate}
              //  playbackRate={playbackRate}
                // Note: Event creation/update/deletion is now handled automatically by useTimelineData hook
                onEventDelete={handleEventDelete}
                config={{
                  //allowCreate: allowEditingState && !readOnly && !disabled,
                  //allowDelete: allowEditingState && !readOnly && !disabled,
                  //allowResize: allowEditingState && !readOnly && !disabled,
                  //allowDrag: allowEditingState && !readOnly && !disabled,
                  enableKeyboardShortcuts: true,
                  enableContextMenu: true,
                  snapToGrid: true,
                  gridInterval: 5, // 5 second grid
                  rowHeight: isMobile ? 40 : 50,
                  cursorColor: "#3b82f6",
                  selectionColor: "#3b82f6",
                  //showTimeScale: true,
                  //enableZoom: true,
                  //enableSelection: !readOnly,
                  //mobileOptimized: isMobile
                }}
            />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Keyboard shortcuts help */}
      <TimelineKeyboardShortcutsHelp
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
      />
    </div>
  )
}

// Export the component directly (React compiler will handle optimizations)
export const NFMTimelineComplete = NFMTimelineCompleteComponent