/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * NFM Timeline Editor - Main wrapper component that integrates react-timeline-editor
 * with NFM-specific functionality and styling
 */

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { Timeline } from './components/timeline';
import { TimelineState, TimelineRow, TimelineEvent } from './interface/timeline';
import { cn } from '@/lib/utils';
import { useTimelineData } from '@/hooks/useTimelineData';
import { createAllEffects } from './effects/modalityEffects';
import { renderTimelineAction } from './effects/eventRenderers';
import { NFMTimelineEditorProps, NFMTimelineConfig, InteractionMode } from '@/types/timelineEditor';
import { NFMTimelineContextMenu, useTimelineContextMenu } from './NFMTimelineContextMenu';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import { useVideoTimelineSync } from '@/hooks/useVideoTimelineSync';
import { useTimelineKeyboardShortcuts, TimelineKeyboardShortcutsHelp } from './NFMTimelineKeyboardShortcuts';
import { useTimelineMouseInteractions } from './hooks/useTimelineMouseInteractions';
import { NFMTimelineRowHeaders } from './NFMTimelineRowHeader';
import { updateModalityStyles, removeModalityStyles } from './utils/dynamicStyles';
import './styles/modality-effects.css';
import { toast } from 'sonner';
import { Inter } from 'next/font/google';
import { on } from 'events';


// Default configuration for NFM timeline
const DEFAULT_CONFIG: NFMTimelineConfig = {
  showGrid: true,
  snapToGrid: true,
  gridInterval: 1, // 1 seconds
  minScale: 10,
  maxScale: 1000,
  defaultScale: 100,
  allowDrag: true,
  allowResize: true,
  allowCreate: true,
  allowDelete: true,
  rowHeight: 40,
  cursorColor: '#ef4444',
  selectionColor: '#3b82f6',
  virtualizeRows: false,
  maxVisibleRows: 50,
  enableKeyboardShortcuts: true,
  enableContextMenu: true,
  enableTooltips: true
};

export function NFMTimelineEditor({
  modalities,
  visibleModalities,
  events,
  currentTime = 0,
  duration = 3600,
  isPlaying = false,
  scale=100,//: externalScale,
  config = {},
  className,
  style,
  //height = 400,
  interactionMode = InteractionMode.VIEW,
  // Event handlers
  onDataChange,
  onEventSelect: onEventSelect,
  onEventEdit,
  onRowSelect,
  onTimeChange,
  onPlayStateChange,
  onPlaybackRateChange,
  onScaleChange,
  onScrollChange,
 // onEventClick: onEventClick,
//  onEventDoubleClick: onEventDoubleClick,
//  onEventContextMenu: onEventContextMenu,
//  onRowClick,
//  onTimelineClick,
  onError,
  onValidationError,
  // New callback for timeline engine
  onTimelineEngineReady,
}: NFMTimelineEditorProps) {

  // Merge configuration with defaults
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);

  // Timeline state reference
  const timelineStateRef = useRef<TimelineState>(null);

  // Notify parent when timeline engine is ready
  useEffect(() => {
    if (timelineStateRef.current && onTimelineEngineReady) {
      onTimelineEngineReady(timelineStateRef.current);
    }
  }, [onTimelineEngineReady]);
  
  // Backend already filters events by visibility, modalities have isVisible property
  const calculatedTimelineHeight = useMemo(() => {
    const height = Math.max(200, Math.min(600, modalities.length * 40 + 8)); // 40px per row + 8px for modality select
    console.log('calculatedTimelineHeight updated:', height);
    return height;
  }, [modalities.length]);

  // Time scale formatter to prevent decimal numbers
  const formatTimeScale = useCallback((timeInSeconds: number) => {
    const totalSeconds = Math.floor(timeInSeconds); // Remove decimals
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
  }, []);
  
  // Internal state
  //const [internalScale, setInternalScale] = useState(finalConfig.defaultScale || 100);
  //const scale = externalScale ?? internalScale;
  const [scrollLeft, setScrollLeft] = useState(0);
  const [gridSnapEnabled, setGridSnapEnabled] = useState(false);
  const [dragLineEnabled, setDragLineEnabled] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    content: string;
    position: { x: number; y: number };
  }>({ visible: false, content: '', position: { x: 0, y: 0 } });
  console.debug('[NFMTimelineEditor] RENDER - Component rendering with ttoltip:', tooltip);
  // DOM ref for mouse interactions
  const timelineDomRef = useRef<HTMLDivElement>(null);

  // Context menu state
  const { contextMenu, openContextMenu, closeContextMenu } = useTimelineContextMenu();

  // Note: Convex mutations are now handled directly in useTimelineData hook

  // Use timeline data hook for data management
  const {
    editorData,
    effects,
    duration: calculatedDuration,
    isDirty,
    isValid,
    errors,
    warnings,
    updateData,
    validate,
    getChanges
  } = useTimelineData(visibleModalities || modalities, events, {
    validateOnChange: true,
    autoSave: true,
    autoSaveDelay: 1000,
    enableUndo: false,
    enableChangeTracking: true//interactionMode === InteractionMode.EDIT
    // Note: Convex mutations are now handled directly in the hook
  })

  // Use provided duration or calculated duration
  const finalDuration = useMemo(() => duration || calculatedDuration || 3600, [duration, calculatedDuration]);

  // Create effects for all modalities and event types, memoized for performance
  const baseEffects = useMemo(() => createAllEffects(modalities), [modalities]);
  const allEffects = useMemo(() => ({ ...baseEffects, ...effects }), [baseEffects, effects]);
  
  // Handle data changes from timeline editor
  const handleDataChange = useCallback((newData: TimelineRow[], immediate = false) => {
    if (interactionMode !== InteractionMode.EDIT) return;
    console.log('[NFMTimelineEditor] Handling data change:', newData, 'immediate:', immediate);
    try {
      // Update local state - persistence is now handled automatically by useTimelineData hook
      updateData(newData, immediate);
      onDataChange?.(newData);
    } catch (error) {
      console.error('Error updating timeline data:', error);
      onError?.(error as Error);
    }
  }, [interactionMode, updateData, onDataChange, onError]);



  // Handle action rendering with custom renderers - moved after timelineStaticConfig definition

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////TIMELINE INTERACTION HANDLERS///////////////////////////////

  // Optimized event handlers to prevent excessive re-renders
  const handleActionClick = useCallback((e: React.MouseEvent, param: { action: TimelineEvent; row: TimelineRow; time: number }) => {
    console.log('[NFMTimelineEditor] Action click:', param);
    e.stopPropagation(); // Prevent row click event

    // Handle selection logic
    if (interactionMode !== InteractionMode.DISABLED) {

      setSelectedAction(param.action.id);
      onEventSelect?.(param.action.id);
    }

    // Call external callback
    //onEventClick?.(param.action, e);
  }, [interactionMode, onEventSelect]);

  const handleActionDoubleClick = useCallback((e: React.MouseEvent, param: { action: TimelineEvent; row: TimelineRow; time: number }) => {
    console.log('[NFMTimelineEditor] Action double click:', param);
    e.stopPropagation(); // Prevent row click event

    // Call external callback
    //onEventDoubleClick?.(param.action, e);
  }, []);

  const handleActionContextMenu = useCallback((e: React.MouseEvent, param: { action: TimelineEvent; row: TimelineRow; time: number }) => {
    console.log('[NFMTimelineEditor] Action context menu:', param);
    e.preventDefault();
    e.stopPropagation();

    if (finalConfig.enableContextMenu) {
      openContextMenu(e, {
        type: 'event',
        event: param.action,
        row: param.row
      });
    }

    // Call external callback
    //onEventContextMenu?.(param.action, e);
  }, [finalConfig.enableContextMenu, openContextMenu]);


  // Handle timeline click for creating new events
  const handleTimelineClick = useCallback((time: number, rowId?: string) => {
    console.log('Timeline click');
    console.log('Time:', time);
    console.log('Row ID:', rowId);
    console.log('allowCreate:', finalConfig.allowCreate);
    if (interactionMode != InteractionMode.EDIT || !finalConfig.allowCreate) return;
  //  onTimelineClick?.(time, rowId);

    // Auto-create event if handler not provided
  //  if (!onTimelineClick && rowId) {
      toast.info(`[DEMO] Creating new event at ${time}s on row ${rowId}`);
    
  }, [interactionMode, finalConfig.allowCreate]);

  // Optimized row click handler
  const handleRowClick = useCallback((e: React.MouseEvent, param: { row: TimelineRow; time: number }) => {
    console.log('[NFMTimelineEditor] Row click:', param);
    const rowId = param.row.id;
    handleTimelineClick(param.time, rowId);
    // Note: onRowClick expects (row, event) but we have (event, param) - need to adapt
  //  onRowClick?.(param.row as any, e);
    return true;
  }, [handleTimelineClick]);

  // Optimized time area click handler
  const handleTimeAreaClick = useCallback((time: number, _e: React.MouseEvent) => {
    console.log('[NFMTimelineEditor] Time area click:', time);
    // when clicking the time strip on top, it will trigger the onTimeChange event
    // const rowId = (e.target as HTMLElement)?.dataset?.rowId;
    // handleTimelineClick(time, rowId);
    return true;
  }, []);

  // Memoized computed values for Timeline props
  const timelineDisableDrag = useMemo(() => {
    return interactionMode !== InteractionMode.EDIT || !finalConfig.allowDrag;
  }, [interactionMode, finalConfig.allowDrag]);

  const timelineStyle = useMemo(() => ({
    '--cursor-color': finalConfig.cursorColor,
    '--selection-color': finalConfig.selectionColor,
    '--row-height': `${finalConfig.rowHeight}px`
  } as React.CSSProperties), [finalConfig.cursorColor, finalConfig.selectionColor, finalConfig.rowHeight]);

  // Static Timeline configuration values (extracted to prevent recreation)
  const timelineStaticConfig = useMemo(() => ({
    scaleWidth: 100,
    startLeft: 10,
    hideCursor: false
  }), []);

  // Handle action rendering with custom renderers
  // NOTE: Event interactions are now handled by EditAction wrapper, not by custom renderers
  // PERFORMANCE: This function is memoized to prevent unnecessary re-renders during mouse movement
  const handleEventRender = useCallback((event: TimelineEvent, row: TimelineRow) => {
    return renderTimelineAction(event, row, {
      isSelected: false,//selectedActions.includes(event.id),
      isHovered: false, // Hover state not implemented to avoid re-renders on mouse movement
      scale,
      height: finalConfig.rowHeight || 40,
      // Timeline parameters for proper pixel width calculation
      scaleWidth: timelineStaticConfig.scaleWidth,
      startLeft: timelineStaticConfig.startLeft,
      // Pass interaction mode to determine diamond vs bar rendering
      interactionMode: interactionMode,
      // Event handlers are removed - they are now handled by EditAction wrapper
      // This ensures proper event propagation through the timeline component hierarchy
    });
  }, [scale, finalConfig.rowHeight, timelineStaticConfig.scaleWidth, timelineStaticConfig.startLeft, interactionMode]);

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////CONTEXT MENU HANDLERS///////////////////////////////

  // Optimized context menu handlers
  const handleContextMenuEdit = useCallback((event: TimelineEvent) => {
    console.log('Edit event:', event);
    onEventEdit?.(event.id);
  }, [onEventEdit]);

  const handleContextMenuDelete = useCallback((event: TimelineEvent) => {
    // Delete event by removing it from the timeline data
    const newData = editorData.map(row => ({
      ...row,
      actions: row.actions.filter(action => action.id !== event.id)
    }));
    handleDataChange(newData, true); // Use immediate save
  }, [editorData, handleDataChange]);

  const handleContextMenuDuplicate = useCallback((event: TimelineEvent) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate event:', event);
  }, []);

  const handleContextMenuPlayFrom = useCallback((time: number) => {
    timelineStateRef.current?.setTime(time);
    timelineStateRef.current?.play({});
    onTimeChange?.(time);
  }, [onTimeChange]);

  const handleContextMenuZoomToEvent = useCallback((event: TimelineEvent) => {
    // Center the timeline on this event
    console.debug('[NFMTimelineEditor] Zoom to event:', event);
    const eventCenter = (event.start + event.end) / 2;
    timelineStateRef.current?.setTime(eventCenter);
    onTimeChange?.(eventCenter);
  }, [onTimeChange]);

  const handleContextMenuCreateEvent = useCallback((time: number, rowId?: string) => {
    handleTimelineClick(time, rowId);
  }, [handleTimelineClick]);

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////



  // Handle validation errors
  useEffect(() => {
    if (!isValid && errors.length > 0) {
      onValidationError?.(errors);
    }
  }, [isValid, errors, onValidationError]);

  // Inject dynamic modality styles
  useEffect(() => {
    updateModalityStyles(visibleModalities || modalities);

    // Cleanup on unmount
    return () => {
      removeModalityStyles();
    };
  }, [visibleModalities, modalities]);
  
  // Keyboard shortcuts integration
  // TODO. Movve back to complete timeline??
  useTimelineKeyboardShortcuts({
    // Playback controls
    onTogglePlayPause: () => {
      const playing = timelineStateRef.current?.isPlaying || false;
    
      if (timelineStateRef.current) {
        if (playing) {
          timelineStateRef.current.pause();
        } else {
          timelineStateRef.current.play({});
        }
      }
      onPlayStateChange?.(playing);
    },
    onPlay: () => {
      timelineStateRef.current?.play({})
      onPlayStateChange?.(true)
    },
    onPause: () =>{
      timelineStateRef.current?.pause()
      onPlayStateChange?.(false)
    },
    onSeekForward: (amount = 5) => {
      console.log('Seek forward by', amount, 'seconds');
      onTimeChange?.(currentTime+ amount);
    
    },
    onSeekBackward: (amount = 5) => {
      if (timelineStateRef.current) {
        const newTime = Math.max(0, timelineStateRef.current.getTime() - amount);
        timelineStateRef.current.setTime(newTime);
        onTimeChange?.(newTime);
      }
    },
    onSeekToStart: () => {
      //timelineStateRef.current?.setTime(0);
      onTimeChange?.(0);
    },
    onSeekToEnd: () => {
      //const endTime = calculatedDuration || 3600;
      //timelineStateRef.current?.setTime(endTime);
      //onTimeChange?.(endTime);
      console.log('Seek to end - not yet implemented');
    },

    // Selection and editing
    /*onSelectAll: () => {
      const allActionIds = editorData.flatMap(row =>
        row.actions.map(action => action.id)
      );
      setSelectedAction(allActionIds);
      onEventSelect?.(allActionIds);
    },
    onDeselectAll: () => {
      setSelectedActions([]);
      setSelectedRows([]);
    },*/
    onDelete: () => {
      if (selectedAction && finalConfig.allowDelete) {
        console.debug('[NFMTimelineEditor] Deleting selected action:', selectedAction);
        // Delete event by removing it from the timeline data
        const newData = editorData.map(row => ({
          ...row,
          actions: row.actions.filter(action => action.id !== selectedAction)
        }));
        handleDataChange(newData, true); // Use immediate save
        setSelectedAction(null);
      }
    },

    // Zoom and view
    onZoomIn: () => {
      if (onScaleChange) {
        onScaleChange(0.83);
      }
    },
    onZoomOut: () => {
      if (onScaleChange) {
        onScaleChange(1.2);
      }
    },
    onResetZoom: () => {
      const newScale = 100;
      if (onScaleChange) {
        onScaleChange(newScale);
      }
    },
    onFitToView: () => {
      // Calculate scale to fit all content
      if (editorData.length > 0) {
        const maxTime = Math.max(...editorData.flatMap(row =>
          row.actions.map(action => action.end)
        ));
        if (maxTime > 0) {
          // Assuming timeline width of 800px and scale relationship
          const newScale = Math.max(10, Math.min(500, (800 / maxTime) * 100));
          if (onScaleChange) {
            onScaleChange(newScale);
          } 
        }
      }
      console.log('Fit to view - not yet implemented');
    },

    // Navigation and scrolling
    onScrollLeft: () => {
      console.log('Scroll left');
      if (timelineStateRef.current?.setScrollLeft) {
        const currentScrollLeft = scrollLeft - 100; // Scroll left by 100px
        const clampedScrollLeft = Math.max(0, currentScrollLeft);
        timelineStateRef.current.setScrollLeft(clampedScrollLeft);
        setScrollLeft(clampedScrollLeft);
      }
    },
    onScrollRight: () => {
      if (timelineStateRef.current?.setScrollLeft) {
        const currentScrollLeft = scrollLeft + 100; // Scroll right by 100px
        timelineStateRef.current.setScrollLeft(currentScrollLeft);
        setScrollLeft(currentScrollLeft);
      }
    },

    // View modes (placeholder implementations)
    onToggleDragLine: () => {
      toast.info(`Drag line ${ dragLineEnabled ? 'disabled' : 'enabled'}`);
      setDragLineEnabled(!dragLineEnabled);
      // TODO: Implement grid toggle functionality
    },
    onToggleSnap: () => {
      toast.info(`Grid snap ${ gridSnapEnabled ? 'disabled' : 'enabled'}`);
      setGridSnapEnabled(!gridSnapEnabled);
      // TODO: Implement snap toggle functionality
    },
    onToggleExpanded: () => {
      console.log('Toggle expanded view - not yet implemented');
      // TODO: Implement expanded view toggle
    },

    // Help
    onToggleHelp: () => {
      setShowKeyboardHelp(!showKeyboardHelp);
    }
  }, {
    enabled: finalConfig.enableKeyboardShortcuts,
    requireFocus: false //currently accept keyboard input anywhere on the page
  });

  // Enhanced mouse interactions
 /* useTimelineMouseInteractions(timelineDomRef, {
    onHorizontalScroll: (deltaX) => {
      if (timelineStateRef.current?.setScrollLeft) {
        const currentScrollLeft = scrollLeft + deltaX;
        const clampedScrollLeft = Math.max(0, currentScrollLeft);
        timelineStateRef.current.setScrollLeft(clampedScrollLeft);
        setScrollLeft(clampedScrollLeft);
      }
    },
    onZoom: (delta, _centerX) => {
      const realDelta = delta > 0 ? 0.75 : 1.5;
      if (onScaleChange) {
        onScaleChange(realDelta);
      }
    },
    onShowTooltip: (event, target) => {
      console.log('onShowTooltip', event, target);
      const actionId = target.dataset.actionId;
      if (actionId) {
        // Find the action data
        const action = editorData.flatMap(row => row.actions).find(a => a.id === actionId);
        if (action) {
          const rect = target.getBoundingClientRect();
          setTooltip({
            visible: true,
            content: `${action.title || 'Event'}\nStart: ${action.start.toFixed(1)}s\nEnd: ${action.end.toFixed(1)}s\nDuration: ${(action.end - action.start).toFixed(1)}s`,
            position: {
              x: rect.left + rect.width / 2,
              y: rect.top - 10
            }
          });
        }
      }
    },
    onHideTooltip: () => {
      setTooltip(prev => ({ ...prev, visible: false }));
    }
  }, {
    enabled: true, // Always enable mouse interactions unless disabled
    enableHorizontalScroll: true,
    enableZoomOnScroll: true,
    enableTooltips: true,//interactionMode !== InteractionMode.EDIT, // Show tooltips when editing is disabled
    requireCtrlForZoom: true
  });*/


  
  return (
    <div
      className={cn(
        'timeline-editor-nfm',
        'relative w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden',
        {
          'opacity-50 pointer-events-none': interactionMode === InteractionMode.DISABLED,
          'border-red-300 dark:border-red-700': !isValid
        },
        className
      )}
      style={{
        height: calculatedTimelineHeight,
        width: '100%', // Ensure full width
        minWidth: '300px', // Minimum usable width
        ...style
      }}
      tabIndex={0} // Make focusable for keyboard shortcuts
    >
      {/* Validation errors display */}
      {!isValid && errors.length > 0 && (
        <div className="absolute top-0 left-0 right-0 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 p-2 z-10">
          <div className="text-sm text-red-600 dark:text-red-400">
            <strong>Validation Errors:</strong>
            <ul className="list-disc list-inside mt-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
      
      {/* Timeline with row headers */}
      <div className="flex">
        {/* Row headers with TimeArea offset - Hidden on mobile */}
        <div className=" md:flex flex-col">
          

          {/* Row headers */}
          <NFMTimelineRowHeaders
            modalities={modalities}
            visibleModalities={visibleModalities}
            events={events}
            currentTime={currentTime}
          />
        </div>

        {/* Main timeline component */}
        <div ref={timelineDomRef} className="flex-1">
          <Timeline
            ref={timelineStateRef}
            editorData={editorData}
            effects={allEffects}
            onChange={handleDataChange}
            getActionRender={handleEventRender}

            // Timeline configuration (optimized with static and memoized values)
            scale={scale}
            scaleWidth={timelineStaticConfig.scaleWidth}
            startLeft={timelineStaticConfig.startLeft}
            rowHeight={finalConfig.rowHeight}
            getScaleRender={formatTimeScale}
            duration={finalDuration}

            // Interaction settings
            hideCursor={timelineStaticConfig.hideCursor}
            gridSnap={gridSnapEnabled}
            dragLine={dragLineEnabled}
            autoScroll={true}
            disableDrag={timelineDisableDrag}

            // Event handlers (all optimized with useCallback)
            onClickAction={handleActionClick}
            onDoubleClickAction={handleActionDoubleClick}
            onContextMenuAction={handleActionContextMenu}
            onClickRow={handleRowClick}
            onClickTimeArea={handleTimeAreaClick}

            // Custom styling (memoized)
            style={timelineStyle}
          />
        </div>
      </div>
      
      {/* Dirty state indicator */}
      {isDirty && interactionMode === InteractionMode.EDIT && (
        <div className="absolute top-2 right-2 w-2 h-2 bg-orange-400 rounded-full"
             title="Unsaved changes" />
      )}

      {/* Context menu - only render when open to prevent unnecessary re-renders */}
      {contextMenu.isOpen && (
        <NFMTimelineContextMenu
          isOpen={contextMenu.isOpen}
          position={contextMenu.position}
          target={contextMenu.target}
          onClose={closeContextMenu}
          onEdit={handleContextMenuEdit}
          onDelete={handleContextMenuDelete}
          onDuplicate={handleContextMenuDuplicate}
          onPlayFrom={handleContextMenuPlayFrom}
          onZoomToEvent={handleContextMenuZoomToEvent}
          onCreateEvent={handleContextMenuCreateEvent}
        />
      )}

      {/* Keyboard shortcuts help */}
      <TimelineKeyboardShortcutsHelp
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
      />

      {/* Enhanced tooltips */}
      {tooltip.visible && (
        <div
          className="absolute z-50 bg-gray-900 text-white text-xs rounded px-2 py-1 pointer-events-none whitespace-pre-line"
          style={{
            left: tooltip.position.x,
            top: tooltip.position.y,
            transform: 'translate(-50%, -100%)'
          }}
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
}

