/**
 * Keyboard shortcuts manager for NFM Timeline Editor
 */

import { useEffect, useCallback } from 'react';
import { TimelineEvent, TimelineRow } from './interface/timeline';
  import { useMemo } from 'react';
export interface KeyboardShortcutHandlers {
  // Playback controls
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlayPause?: () => void;
  onSeekForward?: (amount?: number) => void;
  onSeekBackward?: (amount?: number) => void;
  onSeekToStart?: () => void;
  onSeekToEnd?: () => void;
  
  // Navigation
  onPrevEvent?: () => void;
  onNextEvent?: () => void;
  onScrollLeft?: () => void;
  onScrollRight?: () => void;
  
  // Zoom and view
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onFitToView?: () => void;
  onResetZoom?: () => void;
  
  // Selection
  onSelectAll?: () => void;
  onDeselectAll?: () => void;
  onSelectNext?: () => void;
  onSelectPrev?: () => void;
  
  // Editing
  onDelete?: () => void;
  onCopy?: () => void;
  onCut?: () => void;
  onPaste?: () => void;
  onDuplicate?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  
  // Creation
  onCreate?: () => void;
  onCreateAtCursor?: () => void;
  
  // View modes
  onToggleExpanded?: () => void;
  onToggleDragLine?: () => void;
  onToggleSnap?: () => void;
  
  // Help
  onToggleHelp?: () => void;
}

export interface KeyboardShortcutConfig {
  enabled?: boolean;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  requireFocus?: boolean; // Only trigger when timeline is focused
}

const DEFAULT_CONFIG: Required<KeyboardShortcutConfig> = {
  enabled: true,
  preventDefault: true,
  stopPropagation: true,
  requireFocus: true
};

export function useTimelineKeyboardShortcuts(
  handlers: KeyboardShortcutHandlers,
  config: KeyboardShortcutConfig = {}
) {


  const finalConfig = useMemo(
    () => ({ ...DEFAULT_CONFIG, ...config }),
    [config]
  );

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    console.debug('[TimelineKeyboardShortcuts] Key down:', event.key);
    if (!finalConfig.enabled) return;

    // Check if timeline is focused (if required)
    if (finalConfig.requireFocus) {
      const activeElement = document.activeElement;
      const timelineElement = activeElement?.closest('.timeline-editor-nfm');
      if (!timelineElement) return;
    }

    // Don't trigger shortcuts when typing in inputs
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }

    const { key, ctrlKey, metaKey, shiftKey, altKey } = event;
    const isCtrlOrCmd = ctrlKey || metaKey;
    console.log('Key:', key, 'ctrlKey:', ctrlKey, 'metaKey:', metaKey, 'shiftKey:', shiftKey, 'altKey:', altKey);
    let handled = false;

    // Playback controls
    switch (key) {
      case ' ':
      case 'Space':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onTogglePlayPause?.();
          handled = true;
        }
        break;
        
      case 'p':
      case 'P':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onPlay?.();
          handled = true;
        }
        break;
        
      case 'Escape':
        handlers.onPause?.();
        handlers.onDeselectAll?.();
        handled = true;
        break;
    }

    // Navigation
    switch (key) {
      case 'ArrowLeft':
        if (isCtrlOrCmd) {
          handlers.onSeekBackward?.(30); // 30 seconds
        } else if (shiftKey) {
          handlers.onScrollLeft?.();
        } else {
          handlers.onSeekBackward?.(5); // 5 seconds
        }
        handled = true;
        break;
        
      case 'ArrowRight':
        if (isCtrlOrCmd) {
          handlers.onSeekForward?.(30); // 30 seconds
        } else if (shiftKey) {
          handlers.onScrollRight?.();
        } else {
          handlers.onSeekForward?.(5); // 5 seconds
        }
        handled = true;
        break;
        
      case 'ArrowUp':
        if (shiftKey) {
          handlers.onSelectPrev?.();
        } else {
          handlers.onPrevEvent?.();
        }
        handled = true;
        break;
        
      case 'ArrowDown':
        if (shiftKey) {
          handlers.onSelectNext?.();
        } else {
          handlers.onNextEvent?.();
        }
        handled = true;
        break;
        
      case 'Home':
        handlers.onSeekToStart?.();
        handled = true;
        break;
        
      case 'End':
        handlers.onSeekToEnd?.();
        handled = true;
        break;
    }

    // Zoom and view
    switch (key) {
      case '=':
      case '+':
        if (isCtrlOrCmd) {
          event.preventDefault();
          handlers.onZoomIn?.();
          handled = true;
        }
        break;
        
      case '-':
      case '_':
        if (isCtrlOrCmd) {
          handlers.onZoomOut?.();
          handled = true;
        }
        break;
        
      case '0':
        if (isCtrlOrCmd) {
          handlers.onResetZoom?.();
          handled = true;
        }
        break;
        
      case 'f':
      case 'F':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onFitToView?.();
          handled = true;
        }
        break;
    }

    // Selection
    switch (key) {
      case 'a':
      case 'A':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onSelectAll?.();
          handled = true;
        }
        break;
    }

    // Editing
    switch (key) {
      case 'Delete':
      case 'Backspace':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onDelete?.();
          handled = true;
        }
        break;
        
      case 'c':
      case 'C':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onCopy?.();
          handled = true;
        }
        break;
        
      case 'x':
      case 'X':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onCut?.();
          handled = true;
        }
        break;
        
      case 'v':
      case 'V':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onPaste?.();
          handled = true;
        }
        break;
        
      case 'd':
      case 'D':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onDuplicate?.();
          handled = true;
        }
        break;
        
      case 'z':
      case 'Z':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onUndo?.();
          handled = true;
        } else if (isCtrlOrCmd && shiftKey && !altKey) {
          handlers.onRedo?.();
          handled = true;
        }
        break;
        
      case 'y':
      case 'Y':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onRedo?.();
          handled = true;
        }
        break;
    }

    // Creation
    switch (key) {
      case 'n':
      case 'N':
        if (isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onCreate?.();
          handled = true;
        } else if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onCreateAtCursor?.();
          handled = true;
        }
        break;
    }

    // View modes
    switch (key) {
      case 'e':
      case 'E':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onToggleExpanded?.();
          handled = true;
        }
        break;
        
      case 'g':
      case 'G':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onToggleDragLine?.();
          handled = true;
        }
        break;
        
      case 's':
      case 'S':
        if (!isCtrlOrCmd && !shiftKey && !altKey) {
          handlers.onToggleSnap?.();
          handled = true;
        }
        break;
    }

    // Help
    switch (key) {
      case '?':
      case 'F1':
        if (!isCtrlOrCmd && !altKey) { // && !shiftKey
          handlers.onToggleHelp?.();
          handled = true;
        }
        break;
    }

    if (handled) {
      if (finalConfig.preventDefault) {
        event.preventDefault();
      }
      if (finalConfig.stopPropagation) {
        event.stopPropagation();
      }
    }
  }, [handlers, finalConfig]);

  useEffect(() => {
    if (!finalConfig.enabled) return;

    document.addEventListener('keyup', handleKeyDown);
    return () => document.removeEventListener('keyup', handleKeyDown);
  }, [handleKeyDown, finalConfig.enabled]);
}

/**
 * Keyboard shortcuts help component
 */
export function TimelineKeyboardShortcutsHelp({ 
  isOpen, 
  onClose 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
}) {
  if (!isOpen) return null;

  const shortcuts = [
    { category: 'Playback', items: [
      { key: 'Space', description: 'Play/Pause' },
      { key: 'P', description: 'Play' },
      { key: 'Esc', description: 'Pause & Deselect' },
    ]},
    { category: 'Navigation', items: [
      { key: '← →', description: 'Seek 5 seconds' },
      { key: 'Ctrl + ← →', description: 'Seek 30 seconds' },
      { key: '↑ ↓', description: 'Previous/Next event' },
      { key: 'Shift + ← →', description: 'Scroll timeline' },
      { key: 'Home/End', description: 'Go to start/end' },
    ]},
    { category: 'Zoom & View', items: [
      { key: 'Ctrl + +/-', description: 'Zoom in/out' },
      { key: 'Ctrl + 0', description: 'Reset zoom' },
      { key: 'F', description: 'Fit to view' },
      { key: 'E', description: 'Toggle expanded view' },
      { key: 'G', description: 'Toggle DragLine' },
      { key: 'S', description: 'Toggle snap' },
    ]},
    { category: 'Selection & Editing', items: [
      { key: 'Ctrl + A', description: 'Select all' },
      { key: 'Del/Backspace', description: 'Delete selected' },
      { key: 'Ctrl + C/X/V', description: 'Copy/Cut/Paste' },
      { key: 'Ctrl + D', description: 'Duplicate' },
      { key: 'Ctrl + Z/Y', description: 'Undo/Redo' },
    ]},
    { category: 'Creation', items: [
      { key: 'Ctrl + N', description: 'New event' },
      { key: 'N', description: 'New event at cursor' },
    ]},
    { category: 'Help', items: [
      { key: '? or F1', description: 'Show this help' },
    ]},
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Keyboard Shortcuts</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {shortcuts.map((category) => (
              <div key={category.category}>
                <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 mb-3">
                  {category.category}
                </h3>
                <div className="space-y-2">
                  {category.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-300">{item.description}</span>
                      <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono">
                        {item.key}
                      </kbd>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
