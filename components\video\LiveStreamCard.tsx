"use client"

import { ReactPlayerWrapper } from "./ReactPlayerWrapper"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Volume2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { useVideoTimeline } from "@/components/contexts/VideoTimelineContext"

interface LiveStreamCardProps {
  streamPath: string
  isRecording?: boolean
  title?: string
  operatingRoom?: string
  className?: string
  cardClassName?: string
  contentClassName?: string
  showHeader?: boolean
  maxHeight?: string

  // Video sizing options
  maintainAspectRatio?: boolean
  aspectRatio?: string
  focusMode?: boolean

  // Player configuration
  controls?: boolean
  muted?: boolean
  volume?: number

  // Legacy props (deprecated - now handled by context)
  //currentTime?: number
  //onTimeUpdate?: (time: number) => void
  //isPlaying?: boolean
 // onPlayStateChange?: (playing: boolean) => void
}

export function LiveStreamCard({
  streamPath,
  isRecording = false,
  title = "Inomed Monitoring System",
  operatingRoom,
  className,
  cardClassName,
  contentClassName,
  showHeader = true,
  maxHeight,
  maintainAspectRatio = true,
  aspectRatio = "16/9",
  focusMode = false,
  controls = false,
  muted = false,
  volume = 1,
  // Legacy props - now handled by context
  //currentTime,
  //onTimeUpdate,
  //isPlaying,
  //onPlayStateChange,
}: LiveStreamCardProps) {
  const videoTimeline = useVideoTimeline()


  return (
    <div className={cn("flex-1", className)}>
      <Card className={cn(cardClassName, focusMode && "border-none shadow-none")}>
        {showHeader && !focusMode && (
          <CardHeader className={cn(focusMode && "hidden")}>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              {title}
              {operatingRoom && (
                <Badge variant="outline">{operatingRoom}</Badge>
              )}
              {isRecording && (
                <Badge variant="destructive" className="animate-pulse">
                  🔴 RECORDING
                </Badge>
              )}
              {videoTimeline.isVideoLoading && (
                <Badge variant="secondary">
                  Loading...
                </Badge>
              )}
              {videoTimeline.videoError && (
                <Badge variant="destructive">
                  Error
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className={cn(
          "p-0",
          showHeader && !focusMode && "pt-0",
          focusMode && "p-0",
          contentClassName
        )}>
          <div
            className={cn(
              "relative w-full",
              focusMode && "rounded-none"
            )}
            style={{
              maxHeight: maxHeight || "60vh",
              aspectRatio: maintainAspectRatio ? aspectRatio : undefined,
              height: maintainAspectRatio ? "auto" : maxHeight || "60vh"
            }}
          >
            <ReactPlayerWrapper
              url={streamPath}
              controls={controls}
              muted={muted}
              volume={volume}
              className={cn(
                "w-full h-full overflow-hidden",
                focusMode ? "rounded-none" : "rounded-lg"
              )}
              config={{
                file: {
                  attributes: {
                    crossOrigin: '*',
                  },
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
