import { mutation, query } from "./_generated/server";

export const seedModalityConfigs = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if modalities already exist
    const existingModalities = await ctx.db.query("modalityConfigs").collect();
    if (existingModalities.length > 0) {
      return { success: false, message: "Modalities already exist" };
    }

    const defaultModalities = [
      // Special "ALL" modality for compact timeline view
      {
        name: "ALL",
        displayName: "All Modalities",
        colorCode: "#6b7280", // gray-500
        eventTypes: [
          {
            name: "Manual Event",
            severity: "normal" as const,
            defaultDuration: 15,
            description: "Manually created event"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "EMG",
        displayName: "Electromyography",
        colorCode: "#facc15", // yellow-400
        eventTypes: [
          {
            name: "Burst Activity",
            severity: "warning" as const,
            defaultDuration: 15,
            description: "Increased EMG activity detected"
          },
          {
            name: "Signal Loss",
            severity: "critical" as const,
            defaultDuration: 30,
            description: "Loss of EMG signal"
          },
          {
            name: "Normal Activity",
            severity: "normal" as const,
            defaultDuration: 0,
            description: "Normal EMG activity"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "MEP",
        displayName: "Motor Evoked Potentials",
        colorCode: "#f87171", // red-400
        eventTypes: [
          {
            name: "MEP Loss",
            severity: "critical" as const,
            defaultDuration: 30,
            description: "Complete loss of MEP response"
          },
          {
            name: "MEP Recovery",
            severity: "warning" as const,
            defaultDuration: 60,
            description: "Partial recovery of MEP responses"
          },
          {
            name: "Amplitude Decrease",
            severity: "warning" as const,
            defaultDuration: 20,
            description: "Significant decrease in MEP amplitude"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "SSEP",
        displayName: "Somatosensory Evoked Potentials",
        colorCode: "#4ade80", // green-400
        eventTypes: [
          {
            name: "SSEP Loss",
            severity: "critical" as const,
            defaultDuration: 30,
            description: "Loss of SSEP response"
          },
          {
            name: "Amplitude Decrease",
            severity: "warning" as const,
            defaultDuration: 15,
            description: "Decrease in SSEP amplitude"
          },
          {
            name: "Latency Increase",
            severity: "warning" as const,
            defaultDuration: 10,
            description: "Increase in SSEP latency"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "BAEP",
        displayName: "Brainstem Auditory Evoked Potentials",
        colorCode: "#60a5fa", // blue-400
        eventTypes: [
          {
            name: "Wave V Loss",
            severity: "critical" as const,
            defaultDuration: 20,
            description: "Loss of BAEP Wave V"
          },
          {
            name: "Threshold Change",
            severity: "warning" as const,
            defaultDuration: 10,
            description: "Change in auditory threshold"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "VEP",
        displayName: "Visual Evoked Potentials",
        colorCode: "#a78bfa", // purple-400
        eventTypes: [
          {
            name: "Visual Response Loss",
            severity: "critical" as const,
            defaultDuration: 25,
            description: "Loss of visual evoked response"
          },
          {
            name: "Pattern Reversal Change",
            severity: "warning" as const,
            defaultDuration: 15,
            description: "Change in pattern reversal VEP"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      },
      {
        name: "AEP",
        displayName: "Auditory Evoked Potentials",
        colorCode: "#fb923c", // orange-400
        eventTypes: [
          {
            name: "Auditory Response Loss",
            severity: "critical" as const,
            defaultDuration: 20,
            description: "Loss of auditory evoked response"
          },
          {
            name: "Middle Latency Change",
            severity: "warning" as const,
            defaultDuration: 12,
            description: "Change in middle latency response"
          }
        ],
        isActive: true,
        createdAt: Date.now(),
      }
    ];

    const insertedIds = [];
    for (const modality of defaultModalities) {
      const id = await ctx.db.insert("modalityConfigs", modality);
      insertedIds.push(id);
    }

    return { 
      success: true, 
      message: `Successfully seeded ${insertedIds.length} modality configurations`,
      modalityIds: insertedIds
    };
  },
});

export const getActiveModalityConfigs = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("modalityConfigs")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});
