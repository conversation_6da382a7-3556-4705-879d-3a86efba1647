import { mutation, query } from "./_generated/server";
import {
  partial,
} from "convex-helpers/validators";
import { v } from "convex/values";
import { monitoringEventsCrud } from "./crudHelpers";
import { MonitoringEvents } from "./schema";

export const getMonitoringEvent = query({
  args: { eventId: v.id("monitoringEvents") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.eventId);
  },
});

// Get a single monitoring event with its associated eventType
export const getMonitoringEventWithType = query({
  args: { eventId: v.id("monitoringEvents") },
  handler: async (ctx, args) => {
    const event = await ctx.db.get(args.eventId);
    if (!event) {
      return null;
    }

    // Get the associated eventType
    const eventType = await ctx.db.get(event.eventTypeId);
    
    return {
      ...event,
      eventType
    };
    
  },
});

// Get all monitoring events for a project with their eventTypes
export const getProjectEventsWithTypes = query({
  args: {
    projectId: v.id("projects"),
    sessionId: v.optional(v.id("streamSessions"))
  },
  handler: async (ctx, args) => {
    let eventsQuery = ctx.db
      .query("monitoringEvents")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId));

    if (args.sessionId) {
      eventsQuery = eventsQuery.filter((q) => q.eq(q.field("sessionId"), args.sessionId));
    }

    const events = await eventsQuery.collect();

    // Get eventTypes for all events
    const eventsWithTypes = await Promise.all(
      events.map(async (event) => {
        const eventType = await ctx.db.get(event.eventTypeId);
        return {
          ...event,
          eventType
        };
      })
    );

    return eventsWithTypes;
  },
});

// Create a new monitoring event
export const createMonitoringEvent = mutation({
  args: {
    projectId: v.id("projects"),
    sessionId: v.id("streamSessions"),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    modalityId: v.id("modalityConfigs"),
    eventTypeId: v.id("eventTypes"),
    title: v.string(),
    description: v.string(),
    createdBy: v.id("users")
  },
  handler: async (ctx, args) => {
    // Get the eventType to determine severity
    const eventType = await ctx.db.get(args.eventTypeId);
    if (!eventType) {
      throw new Error("Event type not found");
    }

    // Create monitoring event
    const eventId = await ctx.db.insert("monitoringEvents", {
      ...args,
      //timestamp: args.startTime, // For backward compatibility
      //severity: eventType.severity, // Use eventType's severity
      location: undefined,
      screenshots: [],
      videoClip: undefined,
      reviewerId: undefined,
      reviewStatus: "unreviewed",
      reviewNotes: undefined,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    return eventId;
  },
});

// Update a monitoring event
export const updateMonitoringEvent = mutation({
  args: {
    eventId: v.id("monitoringEvents"),
    ...partial(MonitoringEvents.withoutSystemFields)
  },
  handler: async (ctx, args) => {
    const { eventId, ...patch } = args;
    
    patch.updatedAt = Date.now();

    await ctx.db.patch(eventId, patch);
  },
});

// Delete a monitoring event
export const deleteMonitoringEvent = mutation({
  args: { eventId: v.id("monitoringEvents") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.eventId);
    return { success: true };
  },
});

// Get events by modality with eventTypes
export const getEventsByModality = query({
  args: {
    projectId: v.id("projects"),
    modalityId: v.id("modalityConfigs")
  },
  handler: async (ctx, args) => {
    const events = await ctx.db
      .query("monitoringEvents")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.eq(q.field("modalityId"), args.modalityId))
      .collect();

    // Get eventTypes for all events
    const eventsWithTypes = await Promise.all(
      events.map(async (event) => {
        const eventType = await ctx.db.get(event.eventTypeId);
        return {
          ...event,
          eventType
        };
      })
    );

    return eventsWithTypes;
  },
});

// Get events by time range with eventTypes
export const getEventsByTimeRange = query({
  args: {
    projectId: v.id("projects"),
    startTime: v.number(),
    endTime: v.number()
  },
  handler: async (ctx, args) => {
    const events = await ctx.db
      .query("monitoringEvents")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => 
        q.and(
          q.gte(q.field("startTime"), args.startTime),
          q.lte(q.field("startTime"), args.endTime)
        )
      )
      .collect();

    // Get eventTypes for all events
    const eventsWithTypes = await Promise.all(
      events.map(async (event) => {
        const eventType = await ctx.db.get(event.eventTypeId);
        return {
          ...event,
          eventType
        };
      })
    );

    return eventsWithTypes;
  },
});
