import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Simple, working setup that doesn't try to call other mutations
export const setupEverything = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🚀 Starting quick database setup...");
    
    let modalitiesCount = 0;
    let eventTypesCount = 0;
    
    // Step 1: Setup Modalities
    const existingModalities = await ctx.db.query("modalityConfigs").collect();
    if (existingModalities.length === 0) {
      console.log("📋 Creating modalities...");
      
      const modalities = [
        { name: "ALL", displayName: "All Modalities", colorCode: "#6b7280" },
        { name: "EMG", displayName: "Electromyography", colorCode: "#facc15" },
        { name: "MEP", displayName: "Motor Evoked Potentials", colorCode: "#f87171" },
        { name: "SSEP", displayName: "Somatosensory Evoked Potentials", colorCode: "#4ade80" },
        { name: "BAEP", displayName: "Brainstem Auditory Evoked Potentials", colorCode: "#60a5fa" },
        { name: "VEP", displayName: "Visual Evoked Potentials", colorCode: "#a78bfa" },
        { name: "AEP", displayName: "Auditory Evoked Potentials", colorCode: "#fb923c" }
      ];
      
      for (const modality of modalities) {
        await ctx.db.insert("modalityConfigs", {
          ...modality,
          isActive: true,
          createdAt: Date.now()
        });
        modalitiesCount++;
      }
      console.log(`✅ Created ${modalitiesCount} modalities`);
    } else {
      modalitiesCount = existingModalities.length;
      console.log(`ℹ️ Using existing ${modalitiesCount} modalities`);
    }
    
    // Step 2: Setup Event Types
    const existingEventTypes = await ctx.db.query("eventTypes").collect();
    if (existingEventTypes.length === 0) {
      console.log("🏷️ Creating event types...");
      
      // Get all modalities for event type creation
      const allModalities = await ctx.db.query("modalityConfigs").collect();
      
      for (const modality of allModalities) {
        let eventTypesForModality: {
          name: string;
          severity: "normal" | "warning" | "critical";
          defaultDuration: number;
          description: string;
        }[] = [];
        
        switch (modality.name) {
          case "ALL":
            eventTypesForModality = [
              { name: "Manual Event", severity: "normal", defaultDuration: 15, description: "Manually created event" }
            ];
            break;
          case "EMG":
            eventTypesForModality = [
              { name: "Burst Activity", severity: "warning", defaultDuration: 15, description: "Increased EMG activity detected" },
              { name: "Signal Loss", severity: "critical", defaultDuration: 30, description: "Loss of EMG signal" },
              { name: "Normal Activity", severity: "normal", defaultDuration: 0, description: "Normal EMG activity" }
            ];
            break;
          case "MEP":
            eventTypesForModality = [
              { name: "MEP Loss", severity: "critical", defaultDuration: 30, description: "Complete loss of MEP response" },
              { name: "MEP Recovery", severity: "warning", defaultDuration: 60, description: "Partial recovery of MEP responses" },
              { name: "Amplitude Decrease", severity: "warning", defaultDuration: 20, description: "Significant decrease in MEP amplitude" }
            ];
            break;
          case "SSEP":
            eventTypesForModality = [
              { name: "SSEP Loss", severity: "critical", defaultDuration: 30, description: "Loss of SSEP response" },
              { name: "Amplitude Decrease", severity: "warning", defaultDuration: 15, description: "Decrease in SSEP amplitude" },
              { name: "Latency Increase", severity: "warning", defaultDuration: 10, description: "Increase in SSEP latency" }
            ];
            break;
          case "BAEP":
            eventTypesForModality = [
              { name: "Wave V Loss", severity: "critical", defaultDuration: 20, description: "Loss of BAEP Wave V" },
              { name: "Threshold Change", severity: "warning", defaultDuration: 10, description: "Change in auditory threshold" }
            ];
            break;
          case "VEP":
            eventTypesForModality = [
              { name: "Visual Response Loss", severity: "critical", defaultDuration: 25, description: "Loss of visual evoked response" },
              { name: "Pattern Reversal Change", severity: "warning", defaultDuration: 15, description: "Change in pattern reversal VEP" }
            ];
            break;
          case "AEP":
            eventTypesForModality = [
              { name: "Auditory Response Loss", severity: "critical", defaultDuration: 20, description: "Loss of auditory evoked response" },
              { name: "Middle Latency Change", severity: "warning", defaultDuration: 12, description: "Change in middle latency response" }
            ];
            break;
        }
        
        for (const eventType of eventTypesForModality) {
          await ctx.db.insert("eventTypes", {
            name: eventType.name,
            modalityId: modality._id,
            severity: eventType.severity as "normal" | "warning" | "critical",
            defaultDuration: eventType.defaultDuration,
            description: eventType.description,
            isActive: true,
            createdAt: Date.now()
          });
          eventTypesCount++;
        }
      }
      console.log(`✅ Created ${eventTypesCount} event types`);
    } else {
      eventTypesCount = existingEventTypes.length;
      console.log(`ℹ️ Using existing ${eventTypesCount} event types`);
    }
    
    console.log("🎉 Quick setup completed!");
    
    return {
      success: true,
      message: "Database setup completed successfully",
      summary: {
        modalities: modalitiesCount,
        eventTypes: eventTypesCount,
        note: "Run seed:seedTestProject separately if you need test data"
      }
    };
  }
});

export const checkStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const modalities = await ctx.db.query("modalityConfigs").collect();
    const eventTypes = await ctx.db.query("eventTypes").collect();
    const projects = await ctx.db.query("projects").collect();
    const events = await ctx.db.query("monitoringEvents").collect();
    
    return {
      modalities: modalities.length,
      eventTypes: eventTypes.length,
      projects: projects.length,
      events: events.length,
      isReady: modalities.length > 0 && eventTypes.length > 0,
      hasTestData: projects.length > 0 && events.length > 0
    };
  }
});

export const clearEverything = mutation({
  args: { confirm: v.string() },
  handler: async (ctx, args) => {
    if (args.confirm !== "YES_DELETE_ALL") {
      throw new Error("Must confirm with 'YES_DELETE_ALL'");
    }
    
    console.log("🗑️ Clearing all data...");
    
    // Delete in reverse dependency order
    const events = await ctx.db.query("monitoringEvents").collect();
    for (const event of events) {
      await ctx.db.delete(event._id);
    }
    
    const sessions = await ctx.db.query("streamSessions").collect();
    for (const session of sessions) {
      await ctx.db.delete(session._id);
    }
    
    const projects = await ctx.db.query("projects").collect();
    for (const project of projects) {
      await ctx.db.delete(project._id);
    }
    
    const patients = await ctx.db.query("patients").collect();
    for (const patient of patients) {
      await ctx.db.delete(patient._id);
    }
    
    const users = await ctx.db.query("users").collect();
    for (const user of users) {
      await ctx.db.delete(user._id);
    }
    
    const eventTypes = await ctx.db.query("eventTypes").collect();
    for (const eventType of eventTypes) {
      await ctx.db.delete(eventType._id);
    }
    
    const modalities = await ctx.db.query("modalityConfigs").collect();
    for (const modality of modalities) {
      await ctx.db.delete(modality._id);
    }
    
    console.log("✅ All data cleared");
    
    return {
      success: true,
      message: "All data cleared successfully",
      deleted: {
        events: events.length,
        sessions: sessions.length,
        projects: projects.length,
        patients: patients.length,
        users: users.length,
        eventTypes: eventTypes.length,
        modalities: modalities.length
      }
    };
  }
});
