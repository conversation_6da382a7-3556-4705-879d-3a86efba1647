import { mutation } from "./_generated/server";

export const setupCompleteDatabase = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🚀 Starting complete database setup...");
    
    const results = {
      modalities: { success: false, message: "", count: 0 },
      eventTypes: { success: false, message: "", count: 0 },
      testProject: { success: false, message: "", data: null }
    };

    try {
      // Step 1: Seed Modalities
      console.log("📋 Step 1: Seeding modalities...");
      const existingModalities = await ctx.db.query("modalityConfigs").collect();
      
      if (existingModalities.length === 0) {
        // Import and run modality seeding
        const { seedModalityConfigs } = await import("./modalityConfigs");
        const modalityResult = await seedModalityConfigs(ctx, {});
        results.modalities = {
          success: modalityResult.success,
          message: modalityResult.message,
          count: modalityResult.modalityIds?.length || 0
        };
        console.log("✅ Modalities seeded:", results.modalities);
      } else {
        results.modalities = {
          success: true,
          message: "Modalities already exist",
          count: existingModalities.length
        };
        console.log("ℹ️ Modalities already exist:", existingModalities.length);
      }

      // Step 2: Seed Event Types
      console.log("🏷️ Step 2: Seeding event types...");
      const existingEventTypes = await ctx.db.query("eventTypes").collect();
      
      if (existingEventTypes.length === 0) {
        const { seedEventTypes } = await import("./eventTypes");
        const eventTypesResult = await seedEventTypes(ctx, {});
        results.eventTypes = {
          success: eventTypesResult.success,
          message: eventTypesResult.message,
          count: eventTypesResult.eventTypeIds?.length || 0
        };
        console.log("✅ Event types seeded:", results.eventTypes);
      } else {
        results.eventTypes = {
          success: true,
          message: "Event types already exist",
          count: existingEventTypes.length
        };
        console.log("ℹ️ Event types already exist:", existingEventTypes.length);
      }

      // Step 3: Create Test Project (optional)
      console.log("🧪 Step 3: Creating test project...");
      const existingTestProject = await ctx.db
        .query("projects")
        .filter((q) => q.eq(q.field("projectCode"), "TEST-001"))
        .first();
      
      if (!existingTestProject) {
        const { seedTestProject } = await import("./seed");
        const testProjectResult = await seedTestProject(ctx, {});
        results.testProject = {
          success: testProjectResult.success,
          message: testProjectResult.message,
          data: testProjectResult.data
        };
        console.log("✅ Test project created:", results.testProject);
      } else {
        results.testProject = {
          success: true,
          message: "Test project already exists",
          data: { projectId: existingTestProject._id }
        };
        console.log("ℹ️ Test project already exists");
      }

      console.log("🎉 Database setup completed successfully!");
      
      return {
        success: true,
        message: "Database setup completed successfully",
        results,
        summary: {
          modalities: results.modalities.count,
          eventTypes: results.eventTypes.count,
          testProjectCreated: results.testProject.success
        }
      };

    } catch (error) {
      console.error("❌ Database setup failed:", error);
      return {
        success: false,
        message: `Database setup failed: ${error}`,
        results,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

export const clearAllData = mutation({
  args: { 
    confirmCode: "CLEAR_ALL_DATA" // Safety mechanism
  },
  handler: async (ctx, args) => {
    if (args.confirmCode !== "CLEAR_ALL_DATA") {
      throw new Error("Invalid confirmation code. This operation requires confirmation.");
    }

    console.log("🗑️ Starting complete data cleanup...");
    
    const deletedCounts = {
      events: 0,
      sessions: 0,
      projects: 0,
      patients: 0,
      users: 0,
      eventTypes: 0,
      modalities: 0
    };

    try {
      // Delete monitoring events
      const events = await ctx.db.query("monitoringEvents").collect();
      for (const event of events) {
        await ctx.db.delete(event._id);
      }
      deletedCounts.events = events.length;

      // Delete stream sessions
      const sessions = await ctx.db.query("streamSessions").collect();
      for (const session of sessions) {
        await ctx.db.delete(session._id);
      }
      deletedCounts.sessions = sessions.length;

      // Delete projects
      const projects = await ctx.db.query("projects").collect();
      for (const project of projects) {
        await ctx.db.delete(project._id);
      }
      deletedCounts.projects = projects.length;

      // Delete patients
      const patients = await ctx.db.query("patients").collect();
      for (const patient of patients) {
        await ctx.db.delete(patient._id);
      }
      deletedCounts.patients = patients.length;

      // Delete users
      const users = await ctx.db.query("users").collect();
      for (const user of users) {
        await ctx.db.delete(user._id);
      }
      deletedCounts.users = users.length;

      // Delete event types
      const eventTypes = await ctx.db.query("eventTypes").collect();
      for (const eventType of eventTypes) {
        await ctx.db.delete(eventType._id);
      }
      deletedCounts.eventTypes = eventTypes.length;

      // Delete modalities
      const modalities = await ctx.db.query("modalityConfigs").collect();
      for (const modality of modalities) {
        await ctx.db.delete(modality._id);
      }
      deletedCounts.modalities = modalities.length;

      console.log("✅ All data cleared successfully");
      
      return {
        success: true,
        message: "All data cleared successfully",
        deletedCounts
      };

    } catch (error) {
      console.error("❌ Data cleanup failed:", error);
      return {
        success: false,
        message: `Data cleanup failed: ${error}`,
        deletedCounts,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

export const checkDatabaseStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const status = {
      modalities: await ctx.db.query("modalityConfigs").collect(),
      eventTypes: await ctx.db.query("eventTypes").collect(),
      projects: await ctx.db.query("projects").collect(),
      events: await ctx.db.query("monitoringEvents").collect(),
      users: await ctx.db.query("users").collect(),
      patients: await ctx.db.query("patients").collect(),
      sessions: await ctx.db.query("streamSessions").collect()
    };

    const counts = {
      modalities: status.modalities.length,
      eventTypes: status.eventTypes.length,
      projects: status.projects.length,
      events: status.events.length,
      users: status.users.length,
      patients: status.patients.length,
      sessions: status.sessions.length
    };

    const isSetup = counts.modalities > 0 && counts.eventTypes > 0;
    const hasTestData = counts.projects > 0 && counts.events > 0;

    return {
      counts,
      isSetup,
      hasTestData,
      recommendations: {
        needsSetup: !isSetup,
        needsTestData: isSetup && !hasTestData,
        readyToUse: isSetup && hasTestData
      }
    };
  },
});
