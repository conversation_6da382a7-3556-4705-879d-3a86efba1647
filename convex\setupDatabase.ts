import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const setupCompleteDatabase = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🚀 Starting complete database setup...");
    
    const results = {
      modalities: { success: false, message: "", count: 0 },
      eventTypes: { success: false, message: "", count: 0 },
      testProject: { success: false, message: "", data: null as { projectId?: string } | null }
    };

    try {
      // Step 1: Seed Modalities
      console.log("📋 Step 1: Seeding modalities...");
      const existingModalities = await ctx.db.query("modalityConfigs").collect();
      
      if (existingModalities.length === 0) {
        // Seed modalities directly
        const defaultModalities = [
          {
            name: "ALL",
            displayName: "All Modalities",
            colorCode: "#6b7280",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "EMG",
            displayName: "Electromyography",
            colorCode: "#facc15",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "MEP",
            displayName: "Motor Evoked Potentials",
            colorCode: "#f87171",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "SSEP",
            displayName: "Somatosensory Evoked Potentials",
            colorCode: "#4ade80",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "BAEP",
            displayName: "Brainstem Auditory Evoked Potentials",
            colorCode: "#60a5fa",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "VEP",
            displayName: "Visual Evoked Potentials",
            colorCode: "#a78bfa",
            isActive: true,
            createdAt: Date.now(),
          },
          {
            name: "AEP",
            displayName: "Auditory Evoked Potentials",
            colorCode: "#fb923c",
            isActive: true,
            createdAt: Date.now(),
          }
        ];

        const modalityIds = [];
        for (const modality of defaultModalities) {
          const id = await ctx.db.insert("modalityConfigs", modality);
          modalityIds.push(id);
        }

        results.modalities = {
          success: true,
          message: `Successfully seeded ${modalityIds.length} modalities`,
          count: modalityIds.length
        };
        console.log("✅ Modalities seeded:", results.modalities);
      } else {
        results.modalities = {
          success: true,
          message: "Modalities already exist",
          count: existingModalities.length
        };
        console.log("ℹ️ Modalities already exist:", existingModalities.length);
      }

      // Step 2: Seed Event Types
      console.log("🏷️ Step 2: Seeding event types...");
      const existingEventTypes = await ctx.db.query("eventTypes").collect();
      
      if (existingEventTypes.length === 0) {
        // Get modalities to create event types for them
        const modalities = await ctx.db.query("modalityConfigs").collect();
        if (modalities.length === 0) {
          throw new Error("No modalities found. Modalities must be seeded first.");
        }

        const eventTypesToCreate = [];

        // Create event types for each modality
        for (const modality of modalities) {
          switch (modality.name) {
            case "ALL":
              eventTypesToCreate.push({
                name: "Manual Event",
                modalityId: modality._id,
                severity: "normal" as const,
                defaultDuration: 15,
                description: "Manually created event",
                isActive: true,
                createdAt: Date.now(),
              });
              break;

            case "EMG":
              eventTypesToCreate.push(
                {
                  name: "Burst Activity",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 15,
                  description: "Increased EMG activity detected",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Signal Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 30,
                  description: "Loss of EMG signal",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Normal Activity",
                  modalityId: modality._id,
                  severity: "normal" as const,
                  defaultDuration: 0,
                  description: "Normal EMG activity",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;

            case "MEP":
              eventTypesToCreate.push(
                {
                  name: "MEP Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 30,
                  description: "Complete loss of MEP response",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "MEP Recovery",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 60,
                  description: "Partial recovery of MEP responses",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Amplitude Decrease",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 20,
                  description: "Significant decrease in MEP amplitude",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;

            case "SSEP":
              eventTypesToCreate.push(
                {
                  name: "SSEP Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 30,
                  description: "Loss of SSEP response",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Amplitude Decrease",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 15,
                  description: "Decrease in SSEP amplitude",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Latency Increase",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 10,
                  description: "Increase in SSEP latency",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;

            case "BAEP":
              eventTypesToCreate.push(
                {
                  name: "Wave V Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 20,
                  description: "Loss of BAEP Wave V",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Threshold Change",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 10,
                  description: "Change in auditory threshold",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;

            case "VEP":
              eventTypesToCreate.push(
                {
                  name: "Visual Response Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 25,
                  description: "Loss of visual evoked response",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Pattern Reversal Change",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 15,
                  description: "Change in pattern reversal VEP",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;

            case "AEP":
              eventTypesToCreate.push(
                {
                  name: "Auditory Response Loss",
                  modalityId: modality._id,
                  severity: "critical" as const,
                  defaultDuration: 20,
                  description: "Loss of auditory evoked response",
                  isActive: true,
                  createdAt: Date.now(),
                },
                {
                  name: "Middle Latency Change",
                  modalityId: modality._id,
                  severity: "warning" as const,
                  defaultDuration: 12,
                  description: "Change in middle latency response",
                  isActive: true,
                  createdAt: Date.now(),
                }
              );
              break;
          }
        }

        // Insert all event types
        const insertedIds = [];
        for (const eventType of eventTypesToCreate) {
          const id = await ctx.db.insert("eventTypes", eventType);
          insertedIds.push(id);
        }

        results.eventTypes = {
          success: true,
          message: `Successfully seeded ${insertedIds.length} event types`,
          count: insertedIds.length
        };
        console.log("✅ Event types seeded:", results.eventTypes);
      } else {
        results.eventTypes = {
          success: true,
          message: "Event types already exist",
          count: existingEventTypes.length
        };
        console.log("ℹ️ Event types already exist:", existingEventTypes.length);
      }

      // Step 3: Create Test Project (optional)
      console.log("🧪 Step 3: Creating test project...");
      const existingTestProject = await ctx.db
        .query("projects")
        .filter((q) => q.eq(q.field("projectCode"), "TEST-001"))
        .first();

      if (!existingTestProject) {
        // Create test project inline
        results.testProject = {
          success: true,
          message: "Test project creation skipped - run seed:seedTestProject manually if needed",
          data: null
        };
        console.log("ℹ️ Test project creation skipped");
      } else {
        results.testProject = {
          success: true,
          message: "Test project already exists",
          data: { projectId: existingTestProject._id }
        };
        console.log("ℹ️ Test project already exists");
      }

      console.log("🎉 Database setup completed successfully!");
      
      return {
        success: true,
        message: "Database setup completed successfully",
        results,
        summary: {
          modalities: results.modalities.count,
          eventTypes: results.eventTypes.count,
          testProjectCreated: results.testProject.success
        }
      };

    } catch (error) {
      console.error("❌ Database setup failed:", error);
      return {
        success: false,
        message: `Database setup failed: ${error}`,
        results,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

export const clearAllData = mutation({
  args: {
    confirmCode: v.string()
  },
  handler: async (ctx, args) => {
    if (args.confirmCode !== "CLEAR_ALL_DATA") {
      throw new Error("Invalid confirmation code. This operation requires confirmation.");
    }

    console.log("🗑️ Starting complete data cleanup...");
    
    const deletedCounts = {
      events: 0,
      sessions: 0,
      projects: 0,
      patients: 0,
      users: 0,
      eventTypes: 0,
      modalities: 0
    };

    try {
      // Delete monitoring events
      const events = await ctx.db.query("monitoringEvents").collect();
      for (const event of events) {
        await ctx.db.delete(event._id);
      }
      deletedCounts.events = events.length;

      // Delete stream sessions
      const sessions = await ctx.db.query("streamSessions").collect();
      for (const session of sessions) {
        await ctx.db.delete(session._id);
      }
      deletedCounts.sessions = sessions.length;

      // Delete projects
      const projects = await ctx.db.query("projects").collect();
      for (const project of projects) {
        await ctx.db.delete(project._id);
      }
      deletedCounts.projects = projects.length;

      // Delete patients
      const patients = await ctx.db.query("patients").collect();
      for (const patient of patients) {
        await ctx.db.delete(patient._id);
      }
      deletedCounts.patients = patients.length;

      // Delete users
      const users = await ctx.db.query("users").collect();
      for (const user of users) {
        await ctx.db.delete(user._id);
      }
      deletedCounts.users = users.length;

      // Delete event types
      const eventTypes = await ctx.db.query("eventTypes").collect();
      for (const eventType of eventTypes) {
        await ctx.db.delete(eventType._id);
      }
      deletedCounts.eventTypes = eventTypes.length;

      // Delete modalities
      const modalities = await ctx.db.query("modalityConfigs").collect();
      for (const modality of modalities) {
        await ctx.db.delete(modality._id);
      }
      deletedCounts.modalities = modalities.length;

      console.log("✅ All data cleared successfully");
      
      return {
        success: true,
        message: "All data cleared successfully",
        deletedCounts
      };

    } catch (error) {
      console.error("❌ Data cleanup failed:", error);
      return {
        success: false,
        message: `Data cleanup failed: ${error}`,
        deletedCounts,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

export const checkDatabaseStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const status = {
      modalities: await ctx.db.query("modalityConfigs").collect(),
      eventTypes: await ctx.db.query("eventTypes").collect(),
      projects: await ctx.db.query("projects").collect(),
      events: await ctx.db.query("monitoringEvents").collect(),
      users: await ctx.db.query("users").collect(),
      patients: await ctx.db.query("patients").collect(),
      sessions: await ctx.db.query("streamSessions").collect()
    };

    const counts = {
      modalities: status.modalities.length,
      eventTypes: status.eventTypes.length,
      projects: status.projects.length,
      events: status.events.length,
      users: status.users.length,
      patients: status.patients.length,
      sessions: status.sessions.length
    };

    const isSetup = counts.modalities > 0 && counts.eventTypes > 0;
    const hasTestData = counts.projects > 0 && counts.events > 0;

    return {
      counts,
      isSetup,
      hasTestData,
      recommendations: {
        needsSetup: !isSetup,
        needsTestData: isSetup && !hasTestData,
        readyToUse: isSetup && hasTestData
      }
    };
  },
});
