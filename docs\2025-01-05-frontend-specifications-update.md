# Frontend Specifications Update - January 5, 2025
## Current Implementation Status & Phase 3 Requirements

### 🎯 Overview

This document updates the frontend specifications to reflect the current advanced implementation and outlines requirements for Phase 3.1 (Event Creation & Annotation System).

---

### ✅ Currently Implemented Features

#### 1. Advanced Timeline Integration
**Status**: ✅ COMPLETE - Custom react-timeline-editor integration

**Current Implementation**:
- `NFMTimelineEditor`: Main timeline wrapper with custom react-timeline-editor
- `NFMTimelineControls`: Enhanced controls with navigation, zoom, and playback speed
- `VideoTimelineProvider`: Context for video-timeline synchronization
- Duration-based events (bars vs dots based on zoom level)
- Real-time cursor synchronization with video player
- Horizontal scrolling (vertical disabled)
- Event navigation (next/previous with auto-scroll)
- Keyboard shortcuts (space, arrows, +/-, 1-6 for speed)

**Key Components**:
```typescript
// components/timeline/NFMTimelineEditor.tsx - Main timeline component
// components/timeline/NFMTimelineControls.tsx - Control panel
// components/timeline/NFMTimelineComplete.tsx - Complete timeline wrapper
// components/contexts/VideoTimelineContext.tsx - State management
```

#### 2. Dual Sidebar Architecture
**Status**: ✅ COMPLETE - Responsive dual sidebar system

**Current Implementation**:
- Left sidebar: Primary navigation with live session status
- Right sidebar: Contextual content (session controls, event log)
- Responsive behavior (desktop/tablet/mobile)
- Collapsible sidebars with smooth animations

**Key Components**:
```typescript
// components/layout/sidebar-left.tsx - Primary navigation
// components/layout/sidebar-right.tsx - Contextual content
// components/layout/header.tsx - Header with sidebar toggles
```

#### 3. Video Player Integration
**Status**: ✅ COMPLETE - ReactPlayer with timeline synchronization

**Current Implementation**:
- `ReactPlayerWrapper`: Video player with controls
- WebRTC streaming support (MediaMTX integration)
- Video-timeline synchronization with intelligent thresholds
- Screenshot capture capability
- Fullscreen and picture-in-picture modes

#### 4. Project Management System
**Status**: ✅ COMPLETE - Advanced ShadCN table with filtering

**Current Implementation**:
- Projects table with search, filtering, and sorting
- Project creation and editing modals
- Team assignment and role management
- Patient information integration

---

### 🚧 Phase 3.1 Requirements: Event Creation & Annotation System

#### 1. Event Creation Buttons (NEW - Not Implemented)
**Location**: Live monitoring page, below video player

**Requirements**:
```typescript
// components/events/EventCreationBar.tsx - NEW COMPONENT
interface EventCreationBarProps {
  modalities: TimelineModality[];
  currentTime: number;
  onEventCreate: (eventData: EventCreationData) => void;
  className?: string;
}

interface EventCreationData {
  modalityId: Id<"modalityConfigs">;
  eventType: string;
  severity: "normal" | "warning" | "critical";
  timestamp: number;
}
```

**Layout Specification**:
```
┌─ Event Creation ───────────────────────────────────────────────────────────────┐
│ [🔴 MEP Event] [🟡 EMG Alert] [🟢 SSEP Check] [⚪ DES Stimulation] [+ Custom]  │
│     Critical      Warning       Normal        Procedure          New Event    │
└────────────────────────────────────────────────────────────────────────────────┘
```

**Button Specifications**:
- Height: 48px, Padding: 12px 20px
- Icon: 16px colored circle matching modality
- Based on project's enabled modalities
- One-click event creation at current timeline position
- Hover effects and accessibility support

#### 2. Event Editing Interface (NEW - Not Implemented)
**Location**: Modal or sidebar panel

**Requirements**:
```typescript
// components/events/EventEditForm.tsx - NEW COMPONENT
interface EventEditFormProps {
  event: TimelineEvent;
  modalities: TimelineModality[];
  onSave: (updates: Partial<TimelineEvent>) => void;
  onCancel: () => void;
  onDelete?: () => void;
}
```

**Form Fields**:
- Event title (auto-generated, editable)
- Description (textarea, auto-growing)
- Severity level (dropdown with color coding)
- Location/muscle group (optional text field)
- Modality selection (dropdown)
- Start/end time (for duration events)

#### 3. Timeline Event Integration (PARTIAL - Needs Event Creation)
**Current Status**: Events display correctly, need creation integration

**Requirements**:
- Connect event creation buttons to timeline
- Immediate event rendering after creation
- Real-time synchronization across clients
- Event hover tooltips with details
- Click to edit functionality

#### 4. Real-time Event Synchronization (BACKEND READY)
**Current Status**: Backend functions exist, need frontend integration

**Requirements**:
- Use existing `convex/timeline.ts` functions
- Real-time updates via Convex subscriptions
- Optimistic updates for better UX
- Error handling and retry logic

---

### 📋 Implementation Plan for Phase 3.1

#### Step 1: Event Creation Bar Component (2-3 hours)
1. Create `components/events/EventCreationBar.tsx`
2. Integrate with modality configuration from project
3. Add to live monitoring page layout
4. Connect to VideoTimelineContext for current time

#### Step 2: Event Creation Logic (1-2 hours)
1. Create `hooks/useEventCreation.ts`
2. Connect to Convex `createMonitoringEvent` mutation
3. Implement optimistic updates
4. Add error handling and validation

#### Step 3: Timeline Integration (1-2 hours)
1. Update timeline to show newly created events immediately
2. Ensure real-time synchronization works
3. Test event creation across multiple clients
4. Verify event positioning and styling

#### Step 4: Basic Event Editing (2-3 hours)
1. Create `components/events/EventEditForm.tsx`
2. Implement modal or sidebar editing interface
3. Connect to Convex `updateMonitoringEvent` mutation
4. Add validation and error handling

**Total Estimated Time**: 6-10 hours

---

### 🎨 Design System Updates

#### Event Creation Buttons
```css
.event-creation-button {
  @apply h-12 px-5 rounded-md font-medium text-sm;
  @apply border border-gray-200 bg-white hover:bg-gray-50;
  @apply transition-all duration-200 ease-in-out;
  @apply hover:scale-105 active:scale-95;
}

.event-creation-button.critical {
  @apply border-red-200 text-red-700 hover:bg-red-50;
}

.event-creation-button.warning {
  @apply border-yellow-200 text-yellow-700 hover:bg-yellow-50;
}

.event-creation-button.normal {
  @apply border-green-200 text-green-700 hover:bg-green-50;
}
```

#### Event Severity Indicators
```css
.severity-critical { @apply bg-red-500 text-white; }
.severity-warning { @apply bg-yellow-500 text-black; }
.severity-normal { @apply bg-green-500 text-white; }
```

---

### 🔧 Technical Implementation Notes

#### 1. State Management
- Use existing `VideoTimelineProvider` for timeline state
- Add event creation state to context if needed
- Maintain separation of concerns between video and event state

#### 2. Real-time Updates
- Leverage existing Convex real-time subscriptions
- Use optimistic updates for immediate feedback
- Handle conflicts and error states gracefully

#### 3. Type Safety
- Use schema-derived types from Convex
- Maintain type safety throughout event creation flow
- Validate event data before submission

#### 4. Performance Considerations
- Debounce event creation to prevent spam
- Use React.memo for event creation buttons if needed
- Optimize timeline re-renders during event creation

---

### ✅ Success Criteria for Phase 3.1

**Functional Requirements**:
- [ ] Event creation buttons appear based on enabled modalities
- [ ] One-click event creation at current timeline position
- [ ] Events sync in real-time across all clients
- [ ] Basic event editing works correctly
- [ ] Events display with proper modality colors

**Technical Requirements**:
- [ ] No performance degradation from event operations
- [ ] Proper error handling and validation
- [ ] Type-safe implementation throughout
- [ ] Medical audit trail compliance

**User Experience Requirements**:
- [ ] Intuitive event creation workflow
- [ ] Immediate visual feedback
- [ ] Smooth timeline integration
- [ ] Responsive design maintained

The frontend is well-positioned for Phase 3.1 implementation with a solid foundation and clear requirements.
