# Phase 3.1 Continuation Prompt - January 5, 2025
## Event Creation & Annotation System Implementation

### 🚀 CONTINUATION PROMPT FOR NEXT DEVELOPER

```
I'm continuing NFM development. Phase 2.3.2 is complete and Phase 3.1 (Event Creation & Annotation System) is ready to start.

PROJECT STATUS:
- ✅ Phase 1: Core Infrastructure COMPLETE
- ✅ Phase 2: Video Streaming Core COMPLETE  
- ✅ Phase 2.3.1: Timeline Critical Fixes COMPLETE
- ✅ Phase 2.3.2: Enhanced Timeline Controls COMPLETE
- 🚧 Phase 3.1: Event Creation & Annotation System - READY TO START

CURRENT IMPLEMENTATION STATUS:
- Advanced timeline integration with custom react-timeline-editor
- Video-timeline synchronization with performance optimizations
- Dual sidebar architecture with responsive design
- Convex backend with Table pattern and type safety
- MediaMTX integration for RTSP to WebRTC streaming

WHAT'S WORKING:
- Timeline displays events correctly with modality colors
- Video player synchronizes with timeline cursor
- Real-time data synchronization via Convex
- Project management with team assignments
- Patient information system

WHAT NEEDS TO BE IMPLEMENTED (Phase 3.1):
1. Event Creation Bar component with modality-specific buttons
2. One-click event creation at current timeline position
3. Real-time event synchronization across clients
4. Basic event editing interface
5. Timeline integration for immediate event display

KEY FILES TO REFERENCE:
- docs/2025-01-05-current-implementation-analysis.md - Complete status analysis
- docs/2025-01-05-phase-3-1-implementation-plan.md - Detailed implementation plan
- docs/2025-01-05-frontend-specifications-update.md - Updated frontend requirements
- docs/backend-specifications.md - Updated backend specifications (Table pattern)
- convex/timeline.ts - Existing event functions (createMonitoringEvent, etc.)
- components/timeline/NFMTimelineEditor.tsx - Current timeline implementation
- components/contexts/VideoTimelineContext.tsx - Video-timeline state management

IMPLEMENTATION PRIORITY:
1. START WITH: EventCreationBar component (components/events/EventCreationBar.tsx)
2. THEN: Event creation hook (hooks/useEventCreation.ts)
3. THEN: Timeline integration for immediate event display
4. FINALLY: Basic event editing interface

TECHNICAL REQUIREMENTS:
- Use existing VideoTimelineProvider for current time
- Connect to existing convex/timeline.ts functions
- Maintain type safety with schema-derived types
- Follow established component patterns
- Ensure real-time synchronization works
- Add proper error handling and validation

DESIGN REQUIREMENTS:
- Event creation buttons based on project's enabled modalities
- Color coding: MEP (red/critical), EMG (yellow/warning), SSEP (green/normal)
- Responsive design (desktop/tablet/mobile)
- One-click event creation workflow
- Immediate visual feedback

SUCCESS CRITERIA:
- Event creation buttons appear based on enabled modalities
- One-click event creation at current timeline position
- Events sync in real-time across all clients
- Events display with proper modality colors
- No performance degradation

ESTIMATED TIME: 6-10 hours total for Phase 3.1

Please start with implementing the EventCreationBar component and connecting it to the live monitoring page. The foundation is solid and ready for event creation functionality.
```

---

### 📁 Key Files for Implementation

#### Frontend Components to Create:
1. `components/events/EventCreationBar.tsx` - Main event creation interface
2. `hooks/useEventCreation.ts` - Event creation logic and state management
3. `components/events/EventEditForm.tsx` - Basic event editing (later)

#### Frontend Components to Modify:
1. `app/live-monitoring/page.tsx` - Add EventCreationBar to layout
2. `components/timeline/NFMTimelineEditor.tsx` - Ensure new events display immediately
3. `components/contexts/VideoTimelineContext.tsx` - Add event creation state if needed

#### Backend Functions to Review/Update:
1. `convex/timeline.ts` - Review createMonitoringEvent function
2. Add validation and error handling improvements
3. Ensure real-time synchronization works properly

---

### 🎯 Implementation Steps

#### Step 1: EventCreationBar Component (2-3 hours)
```typescript
// components/events/EventCreationBar.tsx
// - Create modality-based buttons
// - Connect to project's enabled modalities  
// - Implement one-click event creation
// - Add responsive design and accessibility
```

#### Step 2: Event Creation Hook (1-2 hours)
```typescript
// hooks/useEventCreation.ts
// - Connect to Convex createMonitoringEvent mutation
// - Implement optimistic updates
// - Add error handling and validation
// - Manage loading states
```

#### Step 3: Timeline Integration (1-2 hours)
```typescript
// Update timeline to show new events immediately
// Test real-time synchronization across clients
// Verify event positioning and styling
// Ensure performance remains optimal
```

#### Step 4: Basic Event Editing (2-3 hours)
```typescript
// components/events/EventEditForm.tsx
// - Create editing interface (modal or sidebar)
// - Connect to updateMonitoringEvent mutation
// - Add form validation
// - Implement save/cancel functionality
```

---

### 🔧 Technical Notes

#### Current Architecture Strengths:
- Table pattern provides type safety
- VideoTimelineProvider manages state efficiently
- Timeline integration is robust and performant
- Real-time synchronization via Convex is working

#### Key Integration Points:
- Use `videoTimeline.currentTime` for event timestamp
- Connect to existing modality configuration system
- Leverage existing error handling patterns
- Maintain audit trail for medical compliance

#### Performance Considerations:
- Use optimistic updates for immediate feedback
- Debounce event creation to prevent spam
- Maintain efficient re-rendering patterns
- Test with multiple concurrent users

The NFM project is ready for Phase 3.1 implementation with a solid foundation and clear requirements.
