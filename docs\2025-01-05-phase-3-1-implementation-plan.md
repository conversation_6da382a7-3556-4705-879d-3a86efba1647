# Phase 3.1 Implementation Plan - January 5, 2025
## Event Creation & Annotation System

### 🎯 Overview

This document provides a detailed implementation plan for Phase 3.1: Event Creation & Annotation System. The goal is to enable real-time event creation with modality-specific buttons and seamless timeline integration.

---

### 📋 Implementation Tasks

#### Task 1: Event Creation Bar Component
**Estimated Time**: 2-3 hours
**Priority**: High
**Dependencies**: None

**Deliverables**:
1. `components/events/EventCreationBar.tsx` - Main component
2. `hooks/useEventCreation.ts` - Event creation logic
3. Integration with live monitoring page
4. Styling and responsive design

**Implementation Steps**:
1. Create EventCreationBar component with modality-based buttons
2. Connect to project's enabled modalities
3. Implement one-click event creation at current timeline position
4. Add hover effects and accessibility features

#### Task 2: Backend Event Functions Update
**Estimated Time**: 1-2 hours
**Priority**: High
**Dependencies**: None

**Deliverables**:
1. Update `convex/timeline.ts` with proper error handling
2. Add event validation functions
3. Implement real-time synchronization improvements
4. Add audit trail enhancements

**Implementation Steps**:
1. Review existing `createMonitoringEvent` function
2. Add comprehensive validation
3. Improve error handling and user feedback
4. Test real-time synchronization

#### Task 3: Timeline Integration
**Estimated Time**: 1-2 hours
**Priority**: High
**Dependencies**: Task 1, Task 2

**Deliverables**:
1. Connect event creation to timeline display
2. Implement immediate event rendering
3. Add real-time synchronization testing
4. Verify event positioning and styling

**Implementation Steps**:
1. Update timeline to listen for new events
2. Implement optimistic updates
3. Test multi-client synchronization
4. Verify event colors and positioning

#### Task 4: Basic Event Editing
**Estimated Time**: 2-3 hours
**Priority**: Medium
**Dependencies**: Task 1, Task 2, Task 3

**Deliverables**:
1. `components/events/EventEditForm.tsx` - Edit interface
2. Modal or sidebar editing implementation
3. Connect to update mutation
4. Add validation and error handling

**Implementation Steps**:
1. Create event editing form component
2. Implement modal or sidebar interface
3. Connect to Convex update functions
4. Add form validation and error handling

---

### 🔧 Technical Implementation Details

#### EventCreationBar Component
```typescript
// components/events/EventCreationBar.tsx
interface EventCreationBarProps {
  modalities: TimelineModality[];
  currentTime: number;
  onEventCreate: (eventData: EventCreationData) => void;
  disabled?: boolean;
  className?: string;
}

interface EventCreationData {
  modalityId: Id<"modalityConfigs">;
  eventType: string;
  severity: "normal" | "warning" | "critical";
  timestamp: number;
  title?: string;
  description?: string;
}
```

#### Event Creation Hook
```typescript
// hooks/useEventCreation.ts
interface UseEventCreationProps {
  projectId: Id<"projects">;
  sessionId: Id<"streamSessions">;
  currentUserId: Id<"users">;
}

interface UseEventCreationReturn {
  createEvent: (data: EventCreationData) => Promise<void>;
  isCreating: boolean;
  error: string | null;
  clearError: () => void;
}
```

#### Backend Function Updates
```typescript
// convex/timeline.ts - Enhanced createMonitoringEvent
export const createMonitoringEvent = mutation({
  args: {
    projectId: v.id("projects"),
    sessionId: v.id("streamSessions"),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    modalityId: v.id("modalityConfigs"),
    eventType: v.string(),
    severity: v.union(v.literal("normal"), v.literal("warning"), v.literal("critical")),
    title: v.string(),
    description: v.string(),
    createdBy: v.id("users")
  },
  handler: async (ctx, args) => {
    // Enhanced validation
    // Proper error handling
    // Audit trail creation
    // Real-time notification
  }
});
```

---

### 🎨 UI/UX Design Specifications

#### Event Creation Bar Layout
```
┌─ Event Creation ───────────────────────────────────────────────────────────────┐
│ [🔴 MEP Loss] [🟡 EMG Alert] [🟢 SSEP Normal] [⚪ DES Stim] [+ Custom Event]   │
│   Critical      Warning       Normal         Procedure     New Event          │
└────────────────────────────────────────────────────────────────────────────────┘
```

#### Button Specifications
- **Height**: 48px
- **Padding**: 12px 20px
- **Border Radius**: 6px
- **Font**: text-sm font-medium
- **Icon**: 16px colored circle matching modality
- **Hover**: Scale 1.02 with shadow increase
- **Active**: Scale 0.98 with pressed state

#### Color Coding
- **Critical (MEP)**: Red (#ef4444)
- **Warning (EMG)**: Yellow (#eab308)
- **Normal (SSEP)**: Green (#22c55e)
- **Procedure (DES)**: Blue (#3b82f6)
- **Custom**: Gray (#6b7280)

---

### 📱 Responsive Design

#### Desktop (≥1024px)
- Full event creation bar with all buttons visible
- Horizontal layout with proper spacing
- Hover effects and tooltips

#### Tablet (768px - 1023px)
- Compact button layout
- Reduced padding and spacing
- Essential buttons prioritized

#### Mobile (<768px)
- Vertical stacking of buttons
- Larger touch targets (44px minimum)
- Simplified interface with most common events

---

### 🧪 Testing Strategy

#### Unit Tests
- Event creation button rendering
- Event creation hook functionality
- Form validation logic
- Error handling scenarios

#### Integration Tests
- Event creation to timeline display
- Real-time synchronization
- Multi-client event creation
- Backend validation

#### User Acceptance Tests
- One-click event creation workflow
- Event editing functionality
- Timeline integration
- Responsive design verification

---

### 🚀 Deployment Plan

#### Phase 3.1.1: Event Creation Buttons (Week 1)
- Implement EventCreationBar component
- Add to live monitoring page
- Basic event creation functionality
- Initial testing and bug fixes

#### Phase 3.1.2: Timeline Integration (Week 1)
- Connect events to timeline display
- Real-time synchronization
- Multi-client testing
- Performance optimization

#### Phase 3.1.3: Event Editing (Week 2)
- Implement event editing interface
- Form validation and error handling
- User experience refinements
- Final testing and documentation

---

### ✅ Success Criteria

#### Functional Requirements
- [ ] Event creation buttons appear based on enabled modalities
- [ ] One-click event creation at current timeline position
- [ ] Events sync in real-time across all clients
- [ ] Basic event editing works correctly
- [ ] Events display with proper modality colors

#### Technical Requirements
- [ ] No performance degradation from event operations
- [ ] Proper error handling and validation
- [ ] Type-safe implementation throughout
- [ ] Medical audit trail compliance

#### User Experience Requirements
- [ ] Intuitive event creation workflow
- [ ] Immediate visual feedback
- [ ] Smooth timeline integration
- [ ] Responsive design maintained

---

### 📞 Next Steps

1. **Start with Task 1**: Create EventCreationBar component
2. **Parallel development**: Update backend functions while building frontend
3. **Iterative testing**: Test each component as it's built
4. **User feedback**: Get feedback from medical staff early and often

The implementation plan provides a clear path forward for Phase 3.1 with realistic timelines and comprehensive requirements.
