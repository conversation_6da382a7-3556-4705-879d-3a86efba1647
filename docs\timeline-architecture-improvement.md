# Timeline Architecture Improvement: Eliminating Prop Drilling

## Problem

The original architecture had unnecessary prop drilling where Convex mutation callbacks (`onEventCreate`, `onEventUpdate`, `onEventDelete`) were passed down through multiple component layers:

```
NFMTimelineComplete → NFMTimelineEditor → useTimelineData
```

This created:
- **Tight coupling** between components
- **Prop drilling** through multiple layers
- **Redundant code** for wrapper functions
- **Complex type management** between TimelineEvent and Convex Doc types

## Solution

Moved Convex mutations directly into the `useTimelineData` hook, eliminating the need for callback props entirely.

## Changes Made

### 1. Enhanced useTimelineData Hook

**File**: `hooks/useTimelineData.ts`

#### A. Added Direct Convex Integration
```typescript
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useProjectContext } from '@/components/contexts/ProjectContext';

export function useTimelineData(
  modalities: Doc<"modalityConfigs">[],
  events: Doc<"monitoringEvents">[],
  options: UseTimelineDataOptions = {}
): UseTimelineDataReturn {
  // Get project context for mutations
  const { currentProjectId, currentSessionId, currentUserId } = useProjectContext();
  
  // Convex mutations
  const createMonitoringEvent = useMutation(api.timeline.createMonitoringEvent);
  const updateMonitoringEvent = useMutation(api.timeline.updateMonitoringEvent);
  const deleteMonitoringEvent = useMutation(api.timeline.deleteMonitoringEvent);
  const getOrCreateSession = useMutation(api.streamSessions.getOrCreateActiveSession);
```

#### B. Removed Callback Dependencies
```typescript
// Before
export interface UseTimelineDataOptions {
  // ... other options
  onEventCreate?: (event: Partial<Doc<"monitoringEvents">>, modalityId: string) => Promise<void>;
  onEventUpdate?: (event: Partial<Doc<"monitoringEvents">>) => Promise<void>;
  onEventDelete?: (eventId: string) => Promise<void>;
}

// After
export interface UseTimelineDataOptions {
  // ... other options only
  // No callback props needed
}
```

#### C. Direct Mutation Implementation
```typescript
const saveChanges = useCallback(async () => {
  try {
    const changes = getChanges();

    if (changes.hasChanges) {
      // Handle created events
      for (const createdEvent of changes.createdEvents) {
        if (currentProjectId && currentUserId && createdEvent.start !== undefined) {
          const sessionId = currentSessionId || await getOrCreateSession({ projectId: currentProjectId });
          
          if (sessionId) {
            await createMonitoringEvent({
              projectId: currentProjectId,
              sessionId,
              startTime: createdEvent.start,
              endTime: createdEvent.end || createdEvent.start + 15,
              modalityId: createdEvent.modalityId as Id<"modalityConfigs">,
              eventType: createdEvent.eventType || "manual",
              severity: createdEvent.severity || "normal",
              title: createdEvent.title || "New Event",
              description: createdEvent.description || "",
              createdBy: currentUserId
            });
          }
        }
      }

      // Handle updated events
      for (const updatedEvent of changes.updatedEvents) {
        await updateMonitoringEvent({
          eventId: updatedEvent.id as Id<"monitoringEvents">,
          startTime: updatedEvent.start,
          endTime: updatedEvent.end,
          title: updatedEvent.title,
          description: updatedEvent.description,
          severity: updatedEvent.severity,
          eventType: updatedEvent.eventType
        });
      }

      // Handle deleted events
      for (const deletedEventId of changes.deletedEventIds) {
        await deleteMonitoringEvent({
          eventId: deletedEventId
        });
      }
    }

    // Update tracking state
    originalDataRef.current = deepClone(editorData);
    setIsDirty(false);
    setHasUnsavedChanges(false);
    setLastSaved(new Date());
  } catch (error) {
    console.error('Failed to save timeline changes:', error);
    throw error;
  }
}, [editorData, getChanges, currentProjectId, currentSessionId, currentUserId, createMonitoringEvent, updateMonitoringEvent, deleteMonitoringEvent, getOrCreateSession]);
```

### 2. Simplified NFMTimelineEditor

**File**: `components/timeline/NFMTimelineEditor.tsx`

#### A. Removed Callback Props
```typescript
// Before
export function NFMTimelineEditor({
  // ... other props
  onEventCreate,
  onEventUpdate,
  onEventDelete,
  // ... rest
}) {

// After
export function NFMTimelineEditor({
  // ... other props
  // No callback props needed
  // ... rest
}) {
```

#### B. Removed Wrapper Functions
```typescript
// Before
const handleEventCreateForSave = useCallback(async (event: Partial<Doc<"monitoringEvents">>, modalityId: string) => {
  if (onEventCreate) {
    await onEventCreate(event as TimelineEvent, modalityId);
  }
}, [onEventCreate]);

// After
// Note: Convex mutations are now handled directly in useTimelineData hook
```

#### C. Simplified Data Change Handler
```typescript
// Before
const handleDataChange = useCallback((newData: TimelineRow[], immediate = false) => {
  // Update local state
  updateData(newData, immediate);

  // Persist changes to database in the background
  setTimeout(async () => {
    const changes = getChanges();
    if (changes.hasChanges) {
      // Handle created events
      for (const createdEvent of changes.createdEvents) {
        if (onEventCreate) {
          await onEventCreate(createdEvent as TimelineEvent, createdEvent.modalityId as string);
        }
      }
      // ... more persistence logic
    }
  }, immediate ? 0 : 100);
}, [/* many dependencies */]);

// After
const handleDataChange = useCallback((newData: TimelineRow[], immediate = false) => {
  // Update local state - persistence is now handled automatically by useTimelineData hook
  updateData(newData, immediate);
  onDataChange?.(newData);
}, [interactionMode, updateData, onDataChange, onError]);
```

#### D. Updated Delete Handlers
```typescript
// Context menu delete
const handleContextMenuDelete = useCallback((event: TimelineEvent) => {
  // Delete event by removing it from the timeline data
  const newData = editorData.map(row => ({
    ...row,
    actions: row.actions.filter(action => action.id !== event.id)
  }));
  handleDataChange(newData, true); // Use immediate save
}, [editorData, handleDataChange]);

// Keyboard shortcut delete
onDelete: () => {
  if (selectedAction && finalConfig.allowDelete) {
    // Delete event by removing it from the timeline data
    const newData = editorData.map(row => ({
      ...row,
      actions: row.actions.filter(action => action.id !== selectedAction)
    }));
    handleDataChange(newData, true); // Use immediate save
    setSelectedAction(null);
  }
},
```

### 3. Simplified NFMTimelineComplete

**File**: `components/timeline/NFMTimelineComplete.tsx`

#### A. Removed Callback Props
```typescript
// Before
<NFMTimelineEditor
  // ... other props
  onEventCreate={handleCreateEvent}
  onEventUpdate={handleUpdateEvent}
  onEventDelete={handleDeleteEvent}
/>

// After
<NFMTimelineEditor
  // ... other props
  // Note: Event creation/update/deletion is now handled automatically by useTimelineData hook
/>
```

#### B. Removed Handler Functions
```typescript
// Before
const handleCreateEvent = async (event: TimelineEvent, modalityId?: string) => {
  // Complex creation logic with session management
};

// After
// Note: Event creation is now handled automatically by the useTimelineData hook in NFMTimelineEditor
```

## Benefits

### 1. **Reduced Complexity**
- ✅ Eliminated 3 callback props from component interfaces
- ✅ Removed wrapper functions and type conversions
- ✅ Simplified component dependencies

### 2. **Better Separation of Concerns**
- ✅ Data persistence logic is now contained in the data management hook
- ✅ Components focus on UI logic only
- ✅ Convex mutations are co-located with data state management

### 3. **Improved Maintainability**
- ✅ Single source of truth for persistence logic
- ✅ Easier to modify persistence behavior
- ✅ Reduced prop drilling through component tree

### 4. **Enhanced Type Safety**
- ✅ No more complex type conversions between TimelineEvent and Convex Doc types
- ✅ Direct use of proper Convex types in mutations
- ✅ Simplified component prop interfaces

### 5. **Better Performance**
- ✅ Fewer callback dependencies in useCallback hooks
- ✅ Reduced re-renders from prop changes
- ✅ More efficient change detection and persistence

## Architecture Comparison

### Before (Prop Drilling)
```
NFMTimelineComplete
├── createEvent = useMutation(...)
├── updateEvent = useMutation(...)
├── deleteEvent = useMutation(...)
├── handleCreateEvent(event, modalityId) → createEvent(...)
├── handleUpdateEvent(event) → updateEvent(...)
├── handleDeleteEvent(eventId) → deleteEvent(...)
└── NFMTimelineEditor
    ├── onEventCreate={handleCreateEvent}
    ├── onEventUpdate={handleUpdateEvent}
    ├── onEventDelete={handleDeleteEvent}
    ├── handleEventCreateForSave(event, modalityId) → onEventCreate(...)
    ├── handleEventUpdateForSave(event) → onEventUpdate(...)
    ├── handleEventDeleteForSave(eventId) → onEventDelete(...)
    └── useTimelineData(modalities, events, {
        onEventCreate: handleEventCreateForSave,
        onEventUpdate: handleEventUpdateForSave,
        onEventDelete: handleEventDeleteForSave
      })
```

### After (Direct Integration)
```
NFMTimelineComplete
└── NFMTimelineEditor
    └── useTimelineData(modalities, events, options)
        ├── useProjectContext() → { currentProjectId, currentSessionId, currentUserId }
        ├── createMonitoringEvent = useMutation(...)
        ├── updateMonitoringEvent = useMutation(...)
        ├── deleteMonitoringEvent = useMutation(...)
        └── saveChanges() → Direct mutation calls
```

## Migration Impact

- ✅ **Zero breaking changes** for external consumers
- ✅ **Simplified component interfaces** 
- ✅ **Automatic persistence** without manual callback management
- ✅ **Better error handling** centralized in the hook
- ✅ **Consistent behavior** across all timeline operations

This architectural improvement demonstrates the principle of **"co-locating related concerns"** - data persistence logic now lives alongside data state management, resulting in a cleaner, more maintainable codebase.
