# Timeline SaveChanges Implementation

## Overview

The `saveChanges` function in `useTimelineData` has been updated to perform actual Convex mutations for persisting timeline changes to the database.

## Changes Made

### 1. Added Update Mutation

**File**: `convex/timeline.ts`

Added `updateMonitoringEvent` mutation to handle event updates:

```typescript
export const updateMonitoringEvent = mutation({
  args: {
    eventId: v.id("monitoringEvents"),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    modalityId: v.optional(v.id("modalityConfigs")),
    eventType: v.optional(v.string()),
    severity: v.optional(v.union(v.literal("normal"), v.literal("warning"), v.literal("critical"))),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { eventId, ...updates } = args;
    
    // Get existing event to verify it exists
    const existingEvent = await ctx.db.get(eventId);
    if (!existingEvent) {
      throw new Error("Event not found");
    }

    // Prepare update object with only provided fields
    const updateData: any = { updatedAt: Date.now() };

    // Add provided fields to update
    if (updates.startTime !== undefined) {
      updateData.startTime = updates.startTime;
      updateData.timestamp = updates.startTime; // Keep backward compatibility
    }
    if (updates.endTime !== undefined) updateData.endTime = updates.endTime;
    if (updates.modalityId !== undefined) updateData.modalityId = updates.modalityId;
    if (updates.eventType !== undefined) updateData.eventType = updates.eventType;
    if (updates.severity !== undefined) updateData.severity = updates.severity;
    if (updates.title !== undefined) updateData.title = updates.title;
    if (updates.description !== undefined) updateData.description = updates.description;

    await ctx.db.patch(eventId, updateData);
    return { success: true };
  },
});
```

### 2. Enhanced useTimelineData Hook

**File**: `hooks/useTimelineData.ts`

#### A. Added Convex Mutation Callbacks to Options

```typescript
export interface UseTimelineDataOptions {
  // ... existing options
  
  // Convex mutations for persistence
  onEventCreate?: (event: Partial<Doc<"monitoringEvents">>, modalityId: string) => Promise<void>;
  onEventUpdate?: (event: Partial<Doc<"monitoringEvents">>) => Promise<void>;
  onEventDelete?: (eventId: string) => Promise<void>;
}
```

#### B. Implemented Real SaveChanges Function

```typescript
const saveChanges = useCallback(async () => {
  try {
    console.log('Saving timeline changes...');

    // Get the changes that need to be persisted
    const changes = getChanges();

    if (changes.hasChanges) {
      // Handle created events
      for (const createdEvent of changes.createdEvents) {
        if (onEventCreate) {
          await onEventCreate(createdEvent as TimelineEvent, createdEvent.modalityId as string);
        }
      }

      // Handle updated events
      for (const updatedEvent of changes.updatedEvents) {
        if (onEventUpdate) {
          await onEventUpdate(updatedEvent as TimelineEvent);
        }
      }

      // Handle deleted events
      for (const deletedEventId of changes.deletedEventIds) {
        if (onEventDelete) {
          await onEventDelete(deletedEventId);
        }
      }

      console.log(`Timeline changes saved: ${changes.createdEvents.length} created, ${changes.updatedEvents.length} updated, ${changes.deletedEventIds.length} deleted`);
    } else {
      console.log('No timeline changes to save');
    }

    // Update tracking state
    originalDataRef.current = deepClone(editorData);
    setIsDirty(false);
    setHasUnsavedChanges(false);
    setLastSaved(new Date());

    console.log('Timeline changes saved successfully');
  } catch (error) {
    console.error('Failed to save timeline changes:', error);
    throw error;
  }
}, [editorData, getChanges, onEventCreate, onEventUpdate, onEventDelete]);
```

#### C. Added Auto-Save Effect

```typescript
// Auto-save effect
useEffect(() => {
  if (autoSave && isDirty) {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    autoSaveTimeoutRef.current = setTimeout(() => {
      saveChanges();
    }, autoSaveDelay);
  }
}, [isDirty, autoSave, autoSaveDelay, saveChanges]);
```

### 3. Updated NFMTimelineEditor

**File**: `components/timeline/NFMTimelineEditor.tsx`

#### A. Added Wrapper Functions for Type Safety

```typescript
// Wrapper functions for Convex mutations to match expected signatures
const handleEventCreateForSave = useCallback(async (event: Partial<Doc<"monitoringEvents">>, modalityId: string) => {
  if (onEventCreate) {
    await onEventCreate(event as TimelineEvent, modalityId);
  }
}, [onEventCreate]);

const handleEventUpdateForSave = useCallback(async (event: Partial<Doc<"monitoringEvents">>) => {
  if (onEventUpdate) {
    await onEventUpdate(event as TimelineEvent);
  }
}, [onEventUpdate]);

const handleEventDeleteForSave = useCallback(async (eventId: string) => {
  if (onEventDelete) {
    await onEventDelete(eventId);
  }
}, [onEventDelete]);
```

#### B. Passed Callbacks to useTimelineData

```typescript
const {
  // ... other returns
} = useTimelineData(visibleModalities || modalities, events, {
  validateOnChange: true,
  autoSave: true,
  autoSaveDelay: 1000,
  enableUndo: false,
  enableChangeTracking: false,
  // Pass Convex mutation callbacks for persistence
  onEventCreate: handleEventCreateForSave,
  onEventUpdate: handleEventUpdateForSave,
  onEventDelete: handleEventDeleteForSave
})
```

## How It Works

### 1. Change Detection
- The `getChanges()` function analyzes the current `editorData` vs `originalDataRef.current`
- Identifies created, updated, and deleted events
- Returns a structured object with all changes

### 2. Persistence Flow
- **Created Events**: Calls `onEventCreate` with event data and modality ID
- **Updated Events**: Calls `onEventUpdate` with modified event data
- **Deleted Events**: Calls `onEventDelete` with event ID

### 3. Auto-Save Integration
- When `autoSave: true`, changes are automatically saved after `autoSaveDelay` (default 1000ms)
- Uses `isDirty` state to trigger auto-save only when needed
- Debounces multiple rapid changes

### 4. Error Handling
- Catches and logs errors during save operations
- Maintains UI state consistency even if save fails
- Provides detailed error information for debugging

## Usage Example

```typescript
// In a component using NFMTimelineEditor
const createEvent = useMutation(api.timeline.createMonitoringEvent);
const updateEvent = useMutation(api.timeline.updateMonitoringEvent);
const deleteEvent = useMutation(api.timeline.deleteMonitoringEvent);

<NFMTimelineEditor
  // ... other props
  onEventCreate={async (event, modalityId) => {
    await createEvent({
      projectId: currentProject._id,
      modalityId: modalityId as Id<"modalityConfigs">,
      startTime: event.start,
      endTime: event.end,
      eventType: event.eventType || "manual",
      severity: event.severity || "normal",
      title: event.title || "New Event",
      description: event.description || ""
    });
  }}
  onEventUpdate={async (event) => {
    await updateEvent({
      eventId: event.id as Id<"monitoringEvents">,
      startTime: event.start,
      endTime: event.end,
      title: event.title,
      description: event.description,
      severity: event.severity
    });
  }}
  onEventDelete={async (eventId) => {
    await deleteEvent({
      eventId: eventId as Id<"monitoringEvents">
    });
  }}
/>
```

## Testing

A test component has been created at `components/timeline/test/TimelineMutationTest.tsx` to verify the mutations work correctly. This can be temporarily added to any page to test the functionality.

## Benefits

1. **Real Persistence**: Changes are now actually saved to the database
2. **Auto-Save**: Automatic saving reduces risk of data loss
3. **Change Tracking**: Efficient detection of what needs to be saved
4. **Error Handling**: Robust error handling and logging
5. **Type Safety**: Proper TypeScript types for all operations
6. **Performance**: Only saves actual changes, not entire dataset
