"use client";

import { useState, useCallback } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useProjectContext } from '@/components/contexts/ProjectContext';
import { Id } from '@/convex/_generated/dataModel';

export interface EventCreationData {
  modalityId: Id<"modalityConfigs">;
  eventType: string;
  severity: "normal" | "warning" | "critical";
  timestamp: number;
  title?: string;
  description?: string;
  location?: string;
}

export interface UseEventCreationReturn {
  createEvent: (data: EventCreationData) => Promise<void>;
  isCreating: boolean;
  error: string | null;
  clearError: () => void;
}

export function useEventCreation(): UseEventCreationReturn {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get project context
  const { currentProjectId, currentSessionId, currentUserId } = useProjectContext();

  // Convex mutations
  const createMonitoringEvent = useMutation(api.timeline.createMonitoringEvent);
  const getOrCreateSession = useMutation(api.streamSessions.getOrCreateActiveSession);

  const createEvent = useCallback(async (data: EventCreationData) => {
    if (!currentProjectId || !currentUserId) {
      throw new Error('Project or user not available');
    }

    setIsCreating(true);
    setError(null);

    try {
      // Ensure we have an active session
      let sessionId = currentSessionId;
      if (!sessionId) {
        console.log('[useEventCreation] No active session, creating one...');
        sessionId = await getOrCreateSession({ projectId: currentProjectId });
      }

      if (!sessionId) {
        throw new Error('Failed to create or get active session');
      }

      // Validate event data
      if (!data.modalityId || !data.eventType || !data.severity) {
        throw new Error('Missing required event data');
      }

      if (data.timestamp < 0) {
        throw new Error('Invalid timestamp');
      }

      // Create the monitoring event
      const eventId = await createMonitoringEvent({
        projectId: currentProjectId,
        sessionId: sessionId,
        startTime: data.timestamp,
        endTime: undefined, // Point event by default
        modalityId: data.modalityId,
        eventType: data.eventType,
        severity: data.severity,
        title: data.title || `${data.eventType} Event`,
        description: data.description || `${data.eventType} event created at ${Math.floor(data.timestamp)}s`,
        createdBy: currentUserId
      });

      console.log('[useEventCreation] Event created successfully:', eventId);

    } catch (err) {
      console.error('[useEventCreation] Failed to create event:', err);
      
      // Extract error message
      let errorMessage = 'Failed to create event';
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      setError(errorMessage);
      throw err; // Re-throw for caller to handle
    } finally {
      setIsCreating(false);
    }
  }, [
    currentProjectId,
    currentSessionId,
    currentUserId,
    createMonitoringEvent,
    getOrCreateSession
  ]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    createEvent,
    isCreating,
    error,
    clearError
  };
}
